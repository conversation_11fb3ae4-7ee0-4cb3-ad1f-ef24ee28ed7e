/**
 * Navigation Controls Component
 * Provides page navigation controls for documents
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Modal,
  Alert,
} from 'react-native';
import { NavigationControlsProps } from '../../types/reading';

export const NavigationControls: React.FC<NavigationControlsProps> = ({
  navigationState,
  onPageChange,
  onChapterChange: _onChapterChange,
  onGoToPage,
  disabled = false,
}) => {
  const [showGoToPage, setShowGoToPage] = useState(false);
  const [pageInput, setPageInput] = useState('');

  const handlePreviousPage = () => {
    if (navigationState.canGoPrevious && !disabled) {
      onPageChange(navigationState.currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (navigationState.canGoNext && !disabled) {
      onPageChange(navigationState.currentPage + 1);
    }
  };

  const handleGoToPage = () => {
    const pageNumber = parseInt(pageInput, 10);

    if (isNaN(pageNumber)) {
      Alert.alert('Invalid Page', 'Please enter a valid page number');
      return;
    }

    if (pageNumber < 1 || pageNumber > navigationState.totalPages) {
      Alert.alert(
        'Invalid Page',
        `Page number must be between 1 and ${navigationState.totalPages}`,
      );
      return;
    }

    onGoToPage?.(pageNumber);
    setShowGoToPage(false);
    setPageInput('');
  };

  const openGoToPage = () => {
    setPageInput(navigationState.currentPage.toString());
    setShowGoToPage(true);
  };

  return (
    <View style={styles.container}>
      {/* Previous Page Button */}
      <TouchableOpacity
        style={[
          styles.navButton,
          (!navigationState.canGoPrevious || disabled) && styles.disabledButton,
        ]}
        onPress={handlePreviousPage}
        disabled={!navigationState.canGoPrevious || disabled}
      >
        <Text
          style={[
            styles.navButtonText,
            (!navigationState.canGoPrevious || disabled) && styles.disabledText,
          ]}
        >
          ‹
        </Text>
      </TouchableOpacity>

      {/* Page Info */}
      <TouchableOpacity
        style={styles.pageInfo}
        onPress={openGoToPage}
        disabled={disabled}
      >
        <Text style={styles.pageText}>
          {navigationState.currentPage} / {navigationState.totalPages}
        </Text>
        {navigationState.currentChapter && (
          <Text style={styles.chapterText} numberOfLines={1}>
            {navigationState.currentChapter}
          </Text>
        )}
      </TouchableOpacity>

      {/* Next Page Button */}
      <TouchableOpacity
        style={[
          styles.navButton,
          (!navigationState.canGoNext || disabled) && styles.disabledButton,
        ]}
        onPress={handleNextPage}
        disabled={!navigationState.canGoNext || disabled}
      >
        <Text
          style={[
            styles.navButtonText,
            (!navigationState.canGoNext || disabled) && styles.disabledText,
          ]}
        >
          ›
        </Text>
      </TouchableOpacity>

      {/* Go to Page Modal */}
      <Modal
        visible={showGoToPage}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowGoToPage(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Go to Page</Text>

            <TextInput
              style={styles.pageInput}
              value={pageInput}
              onChangeText={setPageInput}
              placeholder={`1 - ${navigationState.totalPages}`}
              keyboardType="numeric"
              selectTextOnFocus={true}
              autoFocus={true}
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowGoToPage(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.goButton]}
                onPress={handleGoToPage}
              >
                <Text style={styles.goButtonText}>Go</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    elevation: 4,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  navButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#6200EE',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  disabledButton: {
    backgroundColor: '#E0E0E0',
    elevation: 0,
    shadowOpacity: 0,
  },
  navButtonText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  disabledText: {
    color: '#9E9E9E',
  },
  pageInfo: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  pageText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  chapterText: {
    fontSize: 14,
    color: '#666666',
    marginTop: 2,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    minWidth: 280,
    elevation: 8,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 16,
  },
  pageInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 6,
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
  },
  goButton: {
    backgroundColor: '#6200EE',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666666',
    textAlign: 'center',
  },
  goButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
  },
});
