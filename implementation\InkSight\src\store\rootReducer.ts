/**
 * Root Reducer Configuration
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import { combineReducers } from '@reduxjs/toolkit';

// Import feature slices
import settingsReducer from './slices/settingsSlice';
import annotationReducer from './slices/annotationSlice';
// import documentsReducer from './slices/documentsSlice';
// import notesReducer from './slices/notesSlice';
// import uiReducer from './slices/uiSlice';
// import handwritingReducer from './slices/handwritingSlice';
// import readingReducer from './slices/readingSlice';

// Temporary placeholder reducer for initial setup
const placeholderReducer = (
  state = { initialized: true },
  action: { type: string; payload?: unknown },
) => {
  switch (action.type) {
    case 'PLACEHOLDER_ACTION':
      return { ...state, placeholder: action.payload };
    default:
      return state;
  }
};

// Root reducer combining all feature slices
const rootReducer = combineReducers({
  // Core feature slices
  settings: settingsReducer,
  annotation: annotationReducer,
  // documents: documentsReducer,
  // notes: notesReducer,
  // ui: uiReducer,

  // AI and reading feature slices
  // handwriting: handwritingReducer,
  // reading: readingReducer,

  // Temporary placeholder until slices are implemented
  placeholder: placeholderReducer,
});

export default rootReducer;
export type RootState = ReturnType<typeof rootReducer>;
