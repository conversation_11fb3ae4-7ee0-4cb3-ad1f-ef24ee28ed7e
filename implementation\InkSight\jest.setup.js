// Jest setup file for React Native testing

// Mock React Native modules that are not available in Jest environment
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');

  // Mock specific modules that might cause issues
  RN.NativeModules = {
    ...RN.NativeModules,
    // Add any specific native module mocks here if needed
  };

  return RN;
});

// Mock react-native-fs if it's used
jest.mock('react-native-fs', () => ({
  readFile: jest.fn(),
  readDir: jest.fn(),
  stat: jest.fn(),
  read: jest.fn(),
  exists: jest.fn(),
  DocumentDirectoryPath: '/mock/documents',
  CachesDirectoryPath: '/mock/caches',
}));

// Mock any other native dependencies
jest.mock('react-native-pdf', () => ({
  default: 'MockedPDF',
}));

// Silence console warnings during tests
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
};

// Setup global test environment
global.__DEV__ = true;
