/**
 * Reading Service Implementation
 * Manages document reading state, progress tracking, and user preferences
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  DocumentViewerState,
  ReadingProgress,
  ReadingPosition,
  ReadingSettings,
  ReadingService,
  ZoomState,
  NavigationState,
  ReadingError,
  ReadingException,
} from '../../types/reading';
import { DocumentParserService } from '../document/DocumentParserService';
import { DocumentFormat } from '../../types/document';

export class ReadingServiceImpl implements ReadingService {
  private documentParserService: DocumentParserService;
  private defaultSettings: ReadingSettings = {
    fontSize: 16,
    fontFamily: 'System',
    lineHeight: 1.5,
    backgroundColor: '#FFFFFF',
    textColor: '#000000',
    theme: 'light',
    autoScroll: false,
    autoScrollSpeed: 1,
    pageTransition: 'slide',
  };

  constructor() {
    this.documentParserService = new DocumentParserService();
  }

  async loadDocument(documentId: string): Promise<DocumentViewerState> {
    try {
      // Load document using parser service
      const parseResult = await this.documentParserService.parseDocument(
        documentId,
        {
          extractText: true,
          extractMetadata: true,
          extractTableOfContents: true,
          generateThumbnail: false,
        },
      );

      if (!parseResult.success || !parseResult.document) {
        throw new ReadingException(
          ReadingError.DOCUMENT_LOAD_FAILED,
          parseResult.error || 'Failed to load document',
          documentId,
        );
      }

      const { metadata, content } = parseResult.document;

      // Load existing reading progress
      const existingProgress = await this.getReadingProgress(documentId);

      // Calculate total pages based on document format
      const totalPages = this.calculateTotalPages(metadata.format, content);

      // Create initial reading progress if none exists
      const readingProgress: ReadingProgress = existingProgress || {
        documentId,
        currentPosition: {
          documentId,
          pageNumber: 1,
          scrollPosition: 0,
          characterOffset: 0,
          timestamp: new Date(),
        },
        totalPages,
        totalCharacters: content.text.length,
        readCharacters: 0,
        progressPercentage: 0,
        readingTime: 0,
        lastReadAt: new Date(),
      };

      // Create initial zoom state
      const zoomState: ZoomState = {
        level: 1.0,
        centerX: 0.5,
        centerY: 0.5,
        fitMode: 'width',
      };

      // Create navigation state
      const navigationState: NavigationState = {
        currentPage: readingProgress.currentPosition.pageNumber || 1,
        totalPages,
        currentChapter: readingProgress.currentPosition.chapterId,
        canGoNext:
          (readingProgress.currentPosition.pageNumber || 1) < totalPages,
        canGoPrevious: (readingProgress.currentPosition.pageNumber || 1) > 1,
      };

      return {
        documentId,
        metadata,
        content,
        isLoading: false,
        readingProgress,
        zoomState,
        navigationState,
        isFullscreen: false,
      };
    } catch (error) {
      throw new ReadingException(
        ReadingError.DOCUMENT_LOAD_FAILED,
        `Failed to load document: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        documentId,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async updateReadingProgress(progress: ReadingProgress): Promise<void> {
    try {
      const key = `reading_progress_${progress.documentId}`;
      await AsyncStorage.setItem(key, JSON.stringify(progress));
    } catch (error) {
      throw new ReadingException(
        ReadingError.PROGRESS_SAVE_FAILED,
        `Failed to save reading progress: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        progress.documentId,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async saveReadingPosition(position: ReadingPosition): Promise<void> {
    try {
      // Update the reading progress with new position
      const existingProgress = await this.getReadingProgress(
        position.documentId,
      );
      if (existingProgress) {
        existingProgress.currentPosition = position;
        existingProgress.lastReadAt = new Date();
        await this.updateReadingProgress(existingProgress);
      }
    } catch (error) {
      throw new ReadingException(
        ReadingError.PROGRESS_SAVE_FAILED,
        `Failed to save reading position: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        position.documentId,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async getReadingHistory(documentId: string): Promise<ReadingProgress[]> {
    try {
      const key = `reading_history_${documentId}`;
      const historyJson = await AsyncStorage.getItem(key);
      return historyJson ? JSON.parse(historyJson) : [];
    } catch (error) {
      console.warn('Failed to load reading history:', error);
      return [];
    }
  }

  async updateSettings(settings: Partial<ReadingSettings>): Promise<void> {
    try {
      const currentSettings = await this.getSettings();
      const updatedSettings = { ...currentSettings, ...settings };
      await AsyncStorage.setItem(
        'reading_settings',
        JSON.stringify(updatedSettings),
      );
    } catch (error) {
      console.warn('Failed to save reading settings:', error);
    }
  }

  async getSettings(): Promise<ReadingSettings> {
    try {
      const settingsJson = await AsyncStorage.getItem('reading_settings');
      return settingsJson
        ? { ...this.defaultSettings, ...JSON.parse(settingsJson) }
        : this.defaultSettings;
    } catch (error) {
      console.warn('Failed to load reading settings:', error);
      return this.defaultSettings;
    }
  }

  private async getReadingProgress(
    documentId: string,
  ): Promise<ReadingProgress | null> {
    try {
      const key = `reading_progress_${documentId}`;
      const progressJson = await AsyncStorage.getItem(key);
      return progressJson ? JSON.parse(progressJson) : null;
    } catch (error) {
      console.warn('Failed to load reading progress:', error);
      return null;
    }
  }

  private calculateTotalPages(
    format: DocumentFormat,
    content: { pages?: unknown[]; chapters?: unknown[]; text: string },
  ): number {
    switch (format) {
      case DocumentFormat.PDF:
        return content.pages?.length || 1;
      case DocumentFormat.EPUB:
        return content.chapters?.length || 1;
      default:
        // For text-based formats, estimate pages based on content length
        const averageWordsPerPage = 250;
        const wordCount = content.text.split(/\s+/).length;
        return Math.max(1, Math.ceil(wordCount / averageWordsPerPage));
    }
  }

  // Utility methods for reading state management
  calculateReadingProgress(
    currentPosition: ReadingPosition,
    totalCharacters: number,
  ): number {
    if (totalCharacters === 0) return 0;
    return Math.min(
      100,
      (currentPosition.characterOffset / totalCharacters) * 100,
    );
  }

  updateNavigationState(
    currentPage: number,
    totalPages: number,
    currentChapter?: string,
  ): NavigationState {
    return {
      currentPage,
      totalPages,
      currentChapter,
      canGoNext: currentPage < totalPages,
      canGoPrevious: currentPage > 1,
    };
  }

  validatePageNumber(page: number, totalPages: number): boolean {
    return page >= 1 && page <= totalPages;
  }

  validateZoomLevel(level: number): boolean {
    return level >= 0.1 && level <= 10.0;
  }
}
