/**
 * Annotation Service
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  Annotation,
  AnnotationType,
  AnnotationCollection,
  AnnotationSearchCriteria,
  AnnotationSearchResult,
  AnnotationStatistics,
  AnnotationExportOptions,
  AnnotationExportFormat,
  AnnotationImportResult,
  AnnotationServiceConfig,
  AnnotationEventType,
  AnnotationEvent,
  IAnnotationService,
} from '../../types/annotation';
import { Logger } from '../Logger';

// Default configuration
const DEFAULT_CONFIG: AnnotationServiceConfig = {
  enableAutoSave: true,
  autoSaveInterval: 5000, // 5 seconds
  maxAnnotationsPerDocument: 1000,
  enableConflictResolution: true,
  defaultHighlightColor: '#FFEB3B' as any,
  defaultHighlightOpacity: 0.3,
  enableEncryption: true,
  compressionEnabled: true,
};

class AnnotationService implements IAnnotationService {
  private config: AnnotationServiceConfig;
  private annotations: Map<string, Annotation> = new Map();
  private documentAnnotations: Map<string, string[]> = new Map();
  private eventListeners: Map<
    AnnotationEventType,
    Array<(event: AnnotationEvent) => void>
  > = new Map();
  private autoSaveTimer?: NodeJS.Timeout;
  private isInitialized = false;

  constructor(config: Partial<AnnotationServiceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // Initialize the service
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadAnnotationsFromStorage();

      if (this.config.enableAutoSave) {
        this.startAutoSave();
      }

      this.isInitialized = true;
      Logger.info('Annotation service initialized', 'annotation');
    } catch (error) {
      Logger.error(
        'Failed to initialize annotation service',
        'annotation',
        {},
        error as Error,
      );
      throw error;
    }
  }

  // CRUD operations
  async createAnnotation(
    annotationData: Omit<Annotation, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<Annotation> {
    try {
      const annotation: Annotation = {
        ...annotationData,
        id: this.generateAnnotationId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      } as Annotation;

      // Check document annotation limit
      const documentId = annotation.documentId;
      const existingCount =
        this.documentAnnotations.get(documentId)?.length || 0;
      if (existingCount >= this.config.maxAnnotationsPerDocument) {
        throw new Error(
          `Maximum annotations per document (${this.config.maxAnnotationsPerDocument}) exceeded`,
        );
      }

      // Store annotation
      this.annotations.set(annotation.id, annotation);

      // Update document annotations index
      const documentAnnotationIds =
        this.documentAnnotations.get(documentId) || [];
      documentAnnotationIds.push(annotation.id);
      this.documentAnnotations.set(documentId, documentAnnotationIds);

      // Persist to storage
      await this.saveAnnotationsToStorage();

      // Emit event
      this.emitEvent(AnnotationEventType.CREATED, annotation);

      Logger.info('Annotation created', 'annotation', {
        id: annotation.id,
        type: annotation.type,
        documentId: annotation.documentId,
      });

      return annotation;
    } catch (error) {
      Logger.error(
        'Failed to create annotation',
        'annotation',
        { annotationData },
        error as Error,
      );
      throw error;
    }
  }

  async getAnnotation(id: string): Promise<Annotation | null> {
    return this.annotations.get(id) || null;
  }

  async updateAnnotation(
    id: string,
    updates: Partial<Annotation>,
  ): Promise<Annotation> {
    try {
      const existingAnnotation = this.annotations.get(id);
      if (!existingAnnotation) {
        throw new Error(`Annotation with id ${id} not found`);
      }

      const updatedAnnotation = {
        ...existingAnnotation,
        ...updates,
        id, // Ensure ID doesn't change
        updatedAt: new Date().toISOString(),
      } as Annotation;

      this.annotations.set(id, updatedAnnotation);
      await this.saveAnnotationsToStorage();

      // Emit event
      this.emitEvent(AnnotationEventType.UPDATED, updatedAnnotation);

      Logger.info('Annotation updated', 'annotation', { id, updates });

      return updatedAnnotation;
    } catch (error) {
      Logger.error(
        'Failed to update annotation',
        'annotation',
        { id, updates },
        error as Error,
      );
      throw error;
    }
  }

  async deleteAnnotation(id: string): Promise<boolean> {
    try {
      const annotation = this.annotations.get(id);
      if (!annotation) {
        return false;
      }

      // Remove from annotations
      this.annotations.delete(id);

      // Remove from document annotations index
      const documentId = annotation.documentId;
      const documentAnnotationIds =
        this.documentAnnotations.get(documentId) || [];
      const updatedIds = documentAnnotationIds.filter(
        annotationId => annotationId !== id,
      );
      this.documentAnnotations.set(documentId, updatedIds);

      await this.saveAnnotationsToStorage();

      // Emit event
      this.emitEvent(AnnotationEventType.DELETED, annotation);

      Logger.info('Annotation deleted', 'annotation', { id, documentId });

      return true;
    } catch (error) {
      Logger.error(
        'Failed to delete annotation',
        'annotation',
        { id },
        error as Error,
      );
      throw error;
    }
  }

  // Collection operations
  async getAnnotationsForDocument(documentId: string): Promise<Annotation[]> {
    try {
      const annotationIds = this.documentAnnotations.get(documentId) || [];
      const annotations = annotationIds
        .map(id => this.annotations.get(id))
        .filter(
          (annotation): annotation is Annotation => annotation !== undefined,
        )
        .sort(
          (a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
        );

      Logger.debug('Retrieved annotations for document', 'annotation', {
        documentId,
        count: annotations.length,
      });

      return annotations;
    } catch (error) {
      Logger.error(
        'Failed to get annotations for document',
        'annotation',
        { documentId },
        error as Error,
      );
      throw error;
    }
  }

  async searchAnnotations(
    criteria: AnnotationSearchCriteria,
  ): Promise<AnnotationSearchResult> {
    try {
      let results = Array.from(this.annotations.values());

      // Apply filters
      if (criteria.documentId) {
        results = results.filter(
          annotation => annotation.documentId === criteria.documentId,
        );
      }

      if (criteria.type) {
        results = results.filter(
          annotation => annotation.type === criteria.type,
        );
      }

      if (criteria.textQuery) {
        const query = criteria.textQuery.toLowerCase();
        results = results.filter(annotation => {
          const searchText = `${annotation.selection.selectedText} ${
            annotation.type === AnnotationType.NOTE
              ? (annotation as any).content || ''
              : ''
          }`.toLowerCase();
          return searchText.includes(query);
        });
      }

      if (criteria.dateRange) {
        const startDate = new Date(criteria.dateRange.start);
        const endDate = new Date(criteria.dateRange.end);
        results = results.filter(annotation => {
          const annotationDate = new Date(annotation.createdAt);
          return annotationDate >= startDate && annotationDate <= endDate;
        });
      }

      // Sort results
      results.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      );

      // Apply pagination
      const offset = criteria.offset || 0;
      const limit = criteria.limit || 50;
      const totalCount = results.length;
      const paginatedResults = results.slice(offset, offset + limit);

      return {
        annotations: paginatedResults,
        totalCount,
        hasMore: offset + limit < totalCount,
      };
    } catch (error) {
      Logger.error(
        'Failed to search annotations',
        'annotation',
        { criteria },
        error as Error,
      );
      throw error;
    }
  }

  async getAnnotationStatistics(
    documentId?: string,
  ): Promise<AnnotationStatistics> {
    try {
      const annotations = documentId
        ? await this.getAnnotationsForDocument(documentId)
        : Array.from(this.annotations.values());

      const byType = annotations.reduce((acc, annotation) => {
        acc[annotation.type] = (acc[annotation.type] || 0) + 1;
        return acc;
      }, {} as Record<AnnotationType, number>);

      const byDocument = annotations.reduce((acc, annotation) => {
        acc[annotation.documentId] = (acc[annotation.documentId] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const dates = annotations.map(a => a.createdAt.split('T')[0]);
      const byDate = dates.reduce((acc, date) => {
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const mostAnnotatedDocument =
        Object.entries(byDocument).sort(([, a], [, b]) => b - a)[0]?.[0] || '';

      const sortedDates = annotations.map(a => a.createdAt).sort();

      return {
        totalAnnotations: annotations.length,
        byType,
        byColor: {} as any, // TODO: Implement color statistics
        byDocument,
        byDate,
        averagePerDocument:
          Object.keys(byDocument).length > 0
            ? annotations.length / Object.keys(byDocument).length
            : 0,
        mostAnnotatedDocument,
        oldestAnnotation: sortedDates[0],
        newestAnnotation: sortedDates[sortedDates.length - 1],
      };
    } catch (error) {
      Logger.error(
        'Failed to get annotation statistics',
        'annotation',
        { documentId },
        error as Error,
      );
      throw error;
    }
  }

  // Export/Import (placeholder implementations)
  async exportAnnotations(
    documentId: string,
    options: AnnotationExportOptions,
  ): Promise<string> {
    try {
      const annotations = await this.getAnnotationsForDocument(documentId);

      // TODO: Implement actual export logic based on format
      const exportData = JSON.stringify(annotations, null, 2);

      Logger.info('Annotations exported', 'annotation', {
        documentId,
        format: options.format,
        count: annotations.length,
      });

      return exportData;
    } catch (error) {
      Logger.error(
        'Failed to export annotations',
        'annotation',
        { documentId, options },
        error as Error,
      );
      throw error;
    }
  }

  async importAnnotations(
    data: string,
    format: AnnotationExportFormat,
  ): Promise<AnnotationImportResult> {
    try {
      // TODO: Implement actual import logic based on format
      const result: AnnotationImportResult = {
        imported: 0,
        skipped: 0,
        errors: [],
        duplicates: 0,
      };

      Logger.info('Annotations imported', 'annotation', { format, result });

      return result;
    } catch (error) {
      Logger.error(
        'Failed to import annotations',
        'annotation',
        { format },
        error as Error,
      );
      throw error;
    }
  }

  // Sync operations (placeholder implementations)
  async syncAnnotations(documentId: string): Promise<any> {
    // TODO: Implement sync logic
    Logger.info('Sync annotations called', 'annotation', { documentId });
    return { documentId, lastSyncAt: new Date().toISOString() };
  }

  async resolveConflict(
    conflictId: string,
    resolution: 'local' | 'remote' | 'merge',
  ): Promise<boolean> {
    // TODO: Implement conflict resolution
    Logger.info('Resolve conflict called', 'annotation', {
      conflictId,
      resolution,
    });
    return true;
  }

  // Event handling
  addEventListener(
    type: AnnotationEventType,
    listener: (event: AnnotationEvent) => void,
  ): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, []);
    }
    this.eventListeners.get(type)!.push(listener);
  }

  removeEventListener(
    type: AnnotationEventType,
    listener: (event: AnnotationEvent) => void,
  ): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  // Private methods
  private emitEvent(type: AnnotationEventType, annotation: Annotation): void {
    const event: AnnotationEvent = {
      type,
      annotation,
      timestamp: new Date().toISOString(),
    };

    const listeners = this.eventListeners.get(type) || [];
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        Logger.error('Error in annotation event listener', 'annotation', {
          type,
          error,
        });
      }
    });
  }

  private generateAnnotationId(): string {
    return `annotation_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;
  }

  private async loadAnnotationsFromStorage(): Promise<void> {
    try {
      const data = await AsyncStorage.getItem('inksight_annotations');
      if (data) {
        const collections: AnnotationCollection[] = JSON.parse(data);

        collections.forEach(collection => {
          const annotationIds: string[] = [];
          collection.annotations.forEach(annotation => {
            this.annotations.set(annotation.id, annotation);
            annotationIds.push(annotation.id);
          });
          this.documentAnnotations.set(collection.documentId, annotationIds);
        });

        Logger.info('Annotations loaded from storage', 'annotation', {
          collections: collections.length,
          totalAnnotations: this.annotations.size,
        });
      }
    } catch (error) {
      Logger.error(
        'Failed to load annotations from storage',
        'annotation',
        {},
        error as Error,
      );
    }
  }

  private async saveAnnotationsToStorage(): Promise<void> {
    try {
      const collections: AnnotationCollection[] = [];

      for (const [
        documentId,
        annotationIds,
      ] of this.documentAnnotations.entries()) {
        const annotations = annotationIds
          .map(id => this.annotations.get(id))
          .filter(
            (annotation): annotation is Annotation => annotation !== undefined,
          );

        if (annotations.length > 0) {
          collections.push({
            documentId,
            annotations,
            lastModified: new Date().toISOString(),
            version: 1,
          });
        }
      }

      await AsyncStorage.setItem(
        'inksight_annotations',
        JSON.stringify(collections),
      );

      Logger.debug('Annotations saved to storage', 'annotation', {
        collections: collections.length,
        totalAnnotations: this.annotations.size,
      });
    } catch (error) {
      Logger.error(
        'Failed to save annotations to storage',
        'annotation',
        {},
        error as Error,
      );
    }
  }

  private startAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }

    this.autoSaveTimer = setInterval(() => {
      this.saveAnnotationsToStorage().catch(error => {
        Logger.error('Auto-save failed', 'annotation', {}, error);
      });
    }, this.config.autoSaveInterval);
  }

  // Cleanup
  destroy(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }
    this.eventListeners.clear();
    this.isInitialized = false;
    Logger.info('Annotation service destroyed', 'annotation');
  }
}

// Create and export singleton instance
export const annotationService = new AnnotationService();

export default AnnotationService;
