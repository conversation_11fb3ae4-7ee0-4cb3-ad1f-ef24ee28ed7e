# Contributing to InkSight

Thank you for your interest in contributing to InkSight! This document provides guidelines and information for contributors.

## 🤝 Code of Conduct

We are committed to providing a welcoming and inclusive environment for all contributors. Please be respectful and professional in all interactions.

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- React Native development environment
- Git
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Setting Up Development Environment

1. Fork the repository on GitHub
2. Clone your fork locally:

   ```bash
   git clone https://github.com/your-username/InkSight.git
   cd InkSight
   ```

3. Navigate to the implementation directory:

   ```bash
   cd implementation/InkSight
   ```

4. Install dependencies:

   ```bash
   npm install
   ```

5. Run quality checks to ensure everything is working:
   ```bash
   npm run lint
   npm run type-check
   npm run test
   ```

## 📝 Development Workflow

### Branch Naming Convention

- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring
- `test/description` - Test improvements

### Making Changes

1. Create a new branch from `main`:

   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make your changes following our coding standards
3. Write or update tests as needed
4. Run quality checks:

   ```bash
   npm run lint:fix
   npm run format
   npm run type-check
   npm run test
   ```

5. Commit your changes with a descriptive message:

   ```bash
   git commit -m "feat: add handwriting recognition for Chinese characters"
   ```

6. Push to your fork:

   ```bash
   git push origin feature/your-feature-name
   ```

7. Create a Pull Request on GitHub

### Commit Message Convention

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes (formatting, etc.)
- `refactor:` - Code refactoring
- `test:` - Adding or updating tests
- `chore:` - Maintenance tasks

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Writing Tests

- Write unit tests for all new functions and components
- Include integration tests for complex features
- Ensure test coverage remains above 80%
- Use descriptive test names and organize tests logically

## 📋 Code Standards

### TypeScript

- Use TypeScript for all new code
- Define proper interfaces and types
- Avoid `any` type unless absolutely necessary
- Use strict TypeScript configuration

### React Native

- Follow React Native best practices
- Use functional components with hooks
- Implement proper error boundaries
- Optimize for performance on both iOS and Android

### Code Style

- Use ESLint and Prettier configurations provided
- Follow Material Design 3 guidelines for UI components
- Write self-documenting code with clear variable names
- Add JSDoc comments for public APIs

### File Organization

```
src/
├── components/          # Reusable UI components
├── screens/            # Screen components
├── services/           # Business logic and API services
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
├── hooks/              # Custom React hooks
└── __tests__/          # Test files
```

## 🔒 Security Guidelines

- Never commit sensitive information (API keys, passwords, etc.)
- Follow secure coding practices
- Report security vulnerabilities privately
- Ensure all data processing remains offline
- Use proper encryption for sensitive data

## 📚 Documentation

### Code Documentation

- Add JSDoc comments for all public functions and classes
- Include usage examples in documentation
- Update README.md for significant changes
- Document breaking changes in CHANGELOG.md

### Architecture Decisions

- Document significant architectural decisions in `docs/architecture/`
- Include rationale and alternatives considered
- Update system diagrams when needed

## 🐛 Bug Reports

When reporting bugs, please include:

- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Device/platform information
- Screenshots or videos if applicable
- Relevant logs or error messages

## 💡 Feature Requests

For new features:

- Check existing issues and discussions first
- Provide clear use case and rationale
- Consider privacy and offline-first principles
- Discuss implementation approach if possible

## 📞 Getting Help

- Check existing documentation in `docs/`
- Search existing issues on GitHub
- Ask questions in GitHub Discussions
- Contact maintainers for urgent issues

## 🏆 Recognition

Contributors will be recognized in:

- CONTRIBUTORS.md file
- Release notes for significant contributions
- GitHub contributor statistics

## 📄 License

By contributing to InkSight, you agree that your contributions will be licensed under the same license as the project.

---

Thank you for contributing to InkSight! Together, we're building the future of privacy-first digital reading and note-taking.
