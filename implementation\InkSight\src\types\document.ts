/**
 * Document types and interfaces for InkSight
 * Supports 9 document formats with unified metadata structure
 */

export enum DocumentFormat {
  EPUB = 'epub',
  PDF = 'pdf',
  TXT = 'txt',
  RTF = 'rtf',
  DOCX = 'docx',
  DOC = 'doc',
  CSV = 'csv',
  MD = 'md',
  HTML = 'html',
}

export interface DocumentMetadata {
  id: string;
  title: string;
  author?: string;
  publisher?: string;
  publishedDate?: string;
  description?: string;
  language?: string;
  isbn?: string;
  pageCount?: number;
  wordCount?: number;
  fileSize: number;
  format: DocumentFormat;
  mimeType: string;
  createdAt: Date;
  modifiedAt: Date;
  lastOpenedAt?: Date;
  readingProgress: number; // 0-100 percentage
  totalReadingTime: number; // in minutes
  coverImageUri?: string;
  thumbnailUri?: string;
}

export interface DocumentChapter {
  id: string;
  title: string;
  href: string;
  level: number; // for nested chapters
  startPage?: number;
  endPage?: number;
  wordCount?: number;
}

export interface DocumentTableOfContents {
  chapters: DocumentChapter[];
  totalChapters: number;
}

export interface DocumentContent {
  text: string;
  html?: string;
  pages?: DocumentPage[];
  chapters?: DocumentChapter[];
}

export interface DocumentPage {
  pageNumber: number;
  content: string;
  wordCount: number;
  characterCount: number;
}

export interface ParsedDocument {
  metadata: DocumentMetadata;
  content: DocumentContent;
  tableOfContents?: DocumentTableOfContents;
  error?: string;
}

export interface DocumentParseOptions {
  extractText: boolean;
  extractMetadata: boolean;
  extractTableOfContents: boolean;
  generateThumbnail: boolean;
  maxTextLength?: number;
  encoding?: string;
}

export interface DocumentParseResult {
  success: boolean;
  document?: ParsedDocument;
  error?: string;
  warnings?: string[];
}

export interface DocumentParser {
  supportedFormats: DocumentFormat[];
  canParse(filePath: string, mimeType?: string): boolean;
  parse(
    filePath: string,
    options?: DocumentParseOptions,
  ): Promise<DocumentParseResult>;
  extractMetadata(filePath: string): Promise<DocumentMetadata>;
  validateDocument(filePath: string): Promise<boolean>;
}

export interface DocumentValidationResult {
  isValid: boolean;
  format?: DocumentFormat;
  mimeType?: string;
  fileSize: number;
  errors: string[];
  warnings: string[];
}

export interface DocumentStats {
  totalDocuments: number;
  formatDistribution: Record<DocumentFormat, number>;
  totalFileSize: number;
  averageFileSize: number;
  totalReadingTime: number;
  mostReadFormat: DocumentFormat;
}

// Reading position and progress tracking
export interface ReadingPosition {
  documentId: string;
  chapterId?: string;
  pageNumber?: number;
  characterOffset: number;
  scrollPosition: number;
  timestamp: Date;
}

export interface ReadingSession {
  id: string;
  documentId: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // in seconds
  pagesRead: number;
  wordsRead: number;
  startPosition: ReadingPosition;
  endPosition?: ReadingPosition;
}

// Document search and indexing
export interface DocumentSearchResult {
  documentId: string;
  chapterId?: string;
  pageNumber?: number;
  snippet: string;
  highlightedSnippet: string;
  relevanceScore: number;
  position: {
    start: number;
    end: number;
  };
}

export interface DocumentSearchOptions {
  query: string;
  caseSensitive?: boolean;
  wholeWords?: boolean;
  regex?: boolean;
  maxResults?: number;
  includeMetadata?: boolean;
}

// Error types for document processing
export enum DocumentError {
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  UNSUPPORTED_FORMAT = 'UNSUPPORTED_FORMAT',
  CORRUPTED_FILE = 'CORRUPTED_FILE',
  PARSING_ERROR = 'PARSING_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  INSUFFICIENT_MEMORY = 'INSUFFICIENT_MEMORY',
  NETWORK_ERROR = 'NETWORK_ERROR',
  ENCRYPTION_ERROR = 'ENCRYPTION_ERROR',
}

export class DocumentProcessingError extends Error {
  constructor(
    public errorType: DocumentError,
    message: string,
    public filePath?: string,
    public originalError?: Error,
  ) {
    super(message);
    this.name = 'DocumentProcessingError';
  }
}
