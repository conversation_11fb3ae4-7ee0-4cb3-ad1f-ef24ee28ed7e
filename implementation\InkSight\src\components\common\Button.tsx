/**
 * Material Design 3 Button Component
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import React from 'react';
import { TouchableOpacity, Text, ViewStyle, TextStyle } from 'react-native';
import { lightTheme, darkTheme } from '../../theme';

export type ButtonVariant = 'filled' | 'outlined' | 'text';
export type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  isDark?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'filled',
  size = 'medium',
  disabled = false,
  isDark = false,
  style,
  textStyle,
}) => {
  const theme = isDark ? darkTheme : lightTheme;

  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: theme.shape.corner.small,
      alignItems: 'center',
      justifyContent: 'center',
    };

    // Size styles
    const sizeStyles: Record<ButtonSize, ViewStyle> = {
      small: {
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        minHeight: 32,
      },
      medium: {
        paddingHorizontal: theme.spacing.lg,
        paddingVertical: theme.spacing.md,
        minHeight: 40,
      },
      large: {
        paddingHorizontal: theme.spacing.xl,
        paddingVertical: theme.spacing.lg,
        minHeight: 48,
      },
    };

    // Variant styles
    const variantStyles: Record<ButtonVariant, ViewStyle> = {
      filled: {
        backgroundColor: disabled ? theme.colors.outline : theme.colors.primary,
        ...theme.elevation.level1,
      },
      outlined: {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: disabled ? theme.colors.outline : theme.colors.primary,
      },
      text: {
        backgroundColor: 'transparent',
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const getTextStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      fontFamily: theme.typography.labelLarge.fontFamily,
      fontSize: theme.typography.labelLarge.fontSize,
      fontWeight: theme.typography.labelLarge.fontWeight as any,
      letterSpacing: theme.typography.labelLarge.letterSpacing,
    };

    // Variant text colors
    const variantTextColors: Record<ButtonVariant, string> = {
      filled: disabled ? theme.colors.onSurfaceVariant : theme.colors.onPrimary,
      outlined: disabled ? theme.colors.onSurfaceVariant : theme.colors.primary,
      text: disabled ? theme.colors.onSurfaceVariant : theme.colors.primary,
    };

    return {
      ...baseStyle,
      color: variantTextColors[variant],
    };
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <Text style={[getTextStyle(), textStyle]}>{title}</Text>
    </TouchableOpacity>
  );
};

export default Button;
