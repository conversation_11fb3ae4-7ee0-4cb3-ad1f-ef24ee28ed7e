/**
 * Annotation Manager Component
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Modal,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import type { AppDispatch } from '../../store';
import { lightTheme, darkTheme } from '../../theme';
import {
  Annotation,
  AnnotationType,
  HighlightAnnotation,
  NoteAnnotation,
} from '../../types/annotation';
import {
  loadAnnotationsForDocument,
  deleteAnnotation,
  setSelectedAnnotation,
  setShowAnnotationPanel,
  selectAnnotationsForCurrentDocument,
  selectSelectedAnnotation,
  selectShowAnnotationPanel,
  selectIsLoading,
} from '../../store/slices/annotationSlice';
// import ColorPicker from './ColorPicker';
import NoteEditor from './NoteEditor';

interface AnnotationManagerProps {
  documentId: string;
  isDark?: boolean;
}

const AnnotationManager: React.FC<AnnotationManagerProps> = ({
  documentId,
  isDark = false,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const theme = isDark ? darkTheme : lightTheme;

  const annotations = useSelector(selectAnnotationsForCurrentDocument);
  const selectedAnnotation = useSelector(selectSelectedAnnotation);
  const showPanel = useSelector(selectShowAnnotationPanel);
  const isLoading = useSelector(selectIsLoading);

  const [showNoteEditor, setShowNoteEditor] = useState(false);
  const [editingNote, setEditingNote] = useState<NoteAnnotation | null>(null);
  const [filterType, setFilterType] = useState<AnnotationType | 'all'>('all');

  // Load annotations when component mounts or document changes
  useEffect(() => {
    if (documentId) {
      dispatch(loadAnnotationsForDocument(documentId));
    }
  }, [documentId, dispatch]);

  // Filter annotations by type
  const filteredAnnotations = annotations.filter(
    annotation => filterType === 'all' || annotation.type === filterType,
  );

  // Handle annotation selection
  const handleAnnotationSelect = useCallback(
    (annotation: Annotation) => {
      dispatch(setSelectedAnnotation(annotation.id));
    },
    [dispatch],
  );

  // Handle annotation deletion
  const handleDeleteAnnotation = useCallback(
    async (annotationId: string) => {
      await dispatch(deleteAnnotation(annotationId));
    },
    [dispatch],
  );

  // Handle edit note
  const handleEditNote = useCallback((note: NoteAnnotation) => {
    setEditingNote(note);
    setShowNoteEditor(true);
  }, []);

  // Handle close note editor
  const handleCloseNoteEditor = useCallback(() => {
    setShowNoteEditor(false);
    setEditingNote(null);
  }, []);

  // Handle close panel
  const handleClosePanel = useCallback(() => {
    dispatch(setShowAnnotationPanel(false));
    dispatch(setSelectedAnnotation(null));
  }, [dispatch]);

  // Get annotation type icon
  const getAnnotationIcon = (type: AnnotationType) => {
    switch (type) {
      case AnnotationType.HIGHLIGHT:
        return '🖍️';
      case AnnotationType.NOTE:
        return '📝';
      case AnnotationType.BOOKMARK:
        return '🔖';
      case AnnotationType.UNDERLINE:
        return '📏';
      case AnnotationType.STRIKETHROUGH:
        return '❌';
      default:
        return '📌';
    }
  };

  // Get annotation preview text
  const getAnnotationPreview = (annotation: Annotation) => {
    if (annotation.type === AnnotationType.NOTE) {
      const note = annotation as NoteAnnotation;
      return note.content.length > 100
        ? `${note.content.substring(0, 100)}...`
        : note.content;
    }
    return annotation.selection.selectedText.length > 50
      ? `${annotation.selection.selectedText.substring(0, 50)}...`
      : annotation.selection.selectedText;
  };

  // Render annotation item
  const renderAnnotationItem = ({ item }: { item: Annotation }) => (
    <TouchableOpacity
      style={[
        styles.annotationItem,
        {
          backgroundColor: theme.colors.surface,
          borderColor: theme.colors.outline,
        },
        selectedAnnotation?.id === item.id && {
          borderColor: theme.colors.primary,
          backgroundColor: theme.colors.primaryContainer,
        },
      ]}
      onPress={() => handleAnnotationSelect(item)}
      activeOpacity={0.7}
    >
      <View style={styles.annotationHeader}>
        <View style={styles.annotationTypeContainer}>
          <Text style={styles.annotationIcon}>
            {getAnnotationIcon(item.type)}
          </Text>
          <Text
            style={[styles.annotationType, { color: theme.colors.onSurface }]}
          >
            {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
          </Text>
        </View>

        {item.type === AnnotationType.HIGHLIGHT && (
          <View
            style={[
              styles.colorIndicator,
              { backgroundColor: (item as HighlightAnnotation).color },
            ]}
          />
        )}
      </View>

      <Text
        style={[
          styles.annotationPreview,
          { color: theme.colors.onSurfaceVariant },
        ]}
      >
        {getAnnotationPreview(item)}
      </Text>

      <View style={styles.annotationFooter}>
        <Text
          style={[
            styles.annotationDate,
            { color: theme.colors.onSurfaceVariant },
          ]}
        >
          {new Date(item.createdAt).toLocaleDateString()}
        </Text>

        <View style={styles.annotationActions}>
          {item.type === AnnotationType.NOTE && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleEditNote(item as NoteAnnotation)}
            >
              <Text
                style={[
                  styles.actionButtonText,
                  { color: theme.colors.primary },
                ]}
              >
                Edit
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteAnnotation(item.id)}
          >
            <Text
              style={[styles.actionButtonText, { color: theme.colors.error }]}
            >
              Delete
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  // Render filter buttons
  const renderFilterButtons = () => (
    <View style={styles.filterContainer}>
      {[
        'all',
        AnnotationType.HIGHLIGHT,
        AnnotationType.NOTE,
        AnnotationType.BOOKMARK,
      ].map(type => (
        <TouchableOpacity
          key={type}
          style={[
            styles.filterButton,
            {
              backgroundColor:
                filterType === type
                  ? theme.colors.primary
                  : theme.colors.surface,
              borderColor: theme.colors.outline,
            },
          ]}
          onPress={() => setFilterType(type as AnnotationType | 'all')}
        >
          <Text
            style={[
              styles.filterButtonText,
              {
                color:
                  filterType === type
                    ? theme.colors.onPrimary
                    : theme.colors.onSurface,
              },
            ]}
          >
            {type === 'all'
              ? 'All'
              : type.charAt(0).toUpperCase() + type.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <>
      <Modal
        visible={showPanel}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleClosePanel}
      >
        <View
          style={[
            styles.container,
            { backgroundColor: theme.colors.background },
          ]}
        >
          {/* Header */}
          <View
            style={[styles.header, { borderBottomColor: theme.colors.outline }]}
          >
            <Text
              style={[styles.headerTitle, { color: theme.colors.onBackground }]}
            >
              Annotations ({filteredAnnotations.length})
            </Text>
            <TouchableOpacity
              onPress={handleClosePanel}
              style={styles.closeButton}
            >
              <Text
                style={[
                  styles.closeButtonText,
                  { color: theme.colors.primary },
                ]}
              >
                Done
              </Text>
            </TouchableOpacity>
          </View>

          {/* Filter buttons */}
          {renderFilterButtons()}

          {/* Annotations list */}
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <Text
                style={[
                  styles.loadingText,
                  { color: theme.colors.onSurfaceVariant },
                ]}
              >
                Loading annotations...
              </Text>
            </View>
          ) : filteredAnnotations.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text
                style={[
                  styles.emptyText,
                  { color: theme.colors.onSurfaceVariant },
                ]}
              >
                No annotations found
              </Text>
              <Text
                style={[
                  styles.emptySubtext,
                  { color: theme.colors.onSurfaceVariant },
                ]}
              >
                Select text to create highlights and notes
              </Text>
            </View>
          ) : (
            <FlatList
              data={filteredAnnotations}
              renderItem={renderAnnotationItem}
              keyExtractor={item => item.id}
              style={styles.annotationsList}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.annotationsListContent}
            />
          )}
        </View>
      </Modal>

      {/* Note Editor Modal */}
      <NoteEditor
        documentId={documentId}
        visible={showNoteEditor}
        onClose={handleCloseNoteEditor}
        isDark={isDark}
        existingNote={editingNote || undefined}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  closeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  annotationsList: {
    flex: 1,
  },
  annotationsListContent: {
    padding: 16,
  },
  annotationItem: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 12,
  },
  annotationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  annotationTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  annotationIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  annotationType: {
    fontSize: 14,
    fontWeight: '500',
  },
  colorIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
  },
  annotationPreview: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  annotationFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  annotationDate: {
    fontSize: 12,
  },
  annotationActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default AnnotationManager;
