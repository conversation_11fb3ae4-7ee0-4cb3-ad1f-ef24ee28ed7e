/**
 * PDF Parser Implementation
 * Handles PDF document parsing using react-native-pdf
 */

import RNFS from 'react-native-fs';
import {
  DocumentFormat,
  DocumentParser,
  DocumentParseResult,
  DocumentParseOptions,
  DocumentMetadata,
  DocumentContent,
  DocumentPage,
  ParsedDocument,
  DocumentError,
  DocumentProcessingError,
} from '../../../types/document';

export class PDFParser implements DocumentParser {
  supportedFormats: DocumentFormat[] = [DocumentFormat.PDF];

  canParse(filePath: string, mimeType?: string): boolean {
    const extension = filePath.toLowerCase().split('.').pop();
    return extension === 'pdf' || mimeType === 'application/pdf';
  }

  async validateDocument(filePath: string): Promise<boolean> {
    try {
      // Check if file exists
      const exists = await RNFS.exists(filePath);
      if (!exists) {
        return false;
      }

      // Check PDF signature
      const header = await RNFS.read(filePath, 8, 0, 'utf8');
      return header.startsWith('%PDF-');
    } catch (error) {
      console.error('PDF validation error:', error);
      return false;
    }
  }

  async parse(
    filePath: string,
    options: DocumentParseOptions = {
      extractText: true,
      extractMetadata: true,
      extractTableOfContents: false,
      generateThumbnail: false,
    },
  ): Promise<DocumentParseResult> {
    try {
      const metadata = await this.extractMetadataFromFile(filePath);
      let content: DocumentContent = { text: '' };

      // Extract content if requested
      if (options.extractText) {
        content = await this.extractContent(filePath, options);
      }

      const parsedDocument: ParsedDocument = {
        metadata,
        content,
      };

      return {
        success: true,
        document: parsedDocument,
      };
    } catch (error) {
      console.error('PDF parsing error:', error);
      return {
        success: false,
        error: `Failed to parse PDF: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      };
    }
  }

  async extractMetadata(filePath: string): Promise<DocumentMetadata> {
    try {
      return await this.extractMetadataFromFile(filePath);
    } catch (error) {
      throw new DocumentProcessingError(
        DocumentError.PARSING_ERROR,
        `Failed to extract PDF metadata: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        filePath,
        error instanceof Error ? error : undefined,
      );
    }
  }

  private async extractMetadataFromFile(
    filePath: string,
  ): Promise<DocumentMetadata> {
    const stats = await RNFS.stat(filePath);

    // Generate unique ID based on file path and modification time
    const id = this.generateDocumentId(filePath, stats.mtime.toString());

    // Basic metadata extraction (enhanced version would use a PDF library)
    const basicMetadata = await this.extractBasicPDFMetadata(filePath);

    return {
      id,
      title: basicMetadata.title || this.getFileNameWithoutExtension(filePath),
      author: basicMetadata.author,
      publisher: basicMetadata.producer,
      publishedDate: basicMetadata.creationDate,
      description: basicMetadata.subject,
      pageCount: basicMetadata.pageCount,
      fileSize: stats.size,
      format: DocumentFormat.PDF,
      mimeType: 'application/pdf',
      createdAt: new Date(stats.ctime),
      modifiedAt: new Date(stats.mtime),
      readingProgress: 0,
      totalReadingTime: 0,
    };
  }

  private async extractBasicPDFMetadata(filePath: string): Promise<{
    title?: string;
    author?: string;
    subject?: string;
    producer?: string;
    creationDate?: string;
    pageCount?: number;
  }> {
    try {
      // Read the first part of the PDF to extract basic metadata
      const fileSize = (await RNFS.stat(filePath)).size;
      const readSize = Math.min(fileSize, 8192); // Read first 8KB
      const content = await RNFS.read(filePath, readSize, 0, 'utf8');

      const metadata: Record<string, unknown> = {};

      // Extract title
      const titleMatch = content.match(/\/Title\s*\(([^)]+)\)/);
      if (titleMatch) {
        metadata.title = this.decodePDFString(titleMatch[1]);
      }

      // Extract author
      const authorMatch = content.match(/\/Author\s*\(([^)]+)\)/);
      if (authorMatch) {
        metadata.author = this.decodePDFString(authorMatch[1]);
      }

      // Extract subject
      const subjectMatch = content.match(/\/Subject\s*\(([^)]+)\)/);
      if (subjectMatch) {
        metadata.subject = this.decodePDFString(subjectMatch[1]);
      }

      // Extract producer
      const producerMatch = content.match(/\/Producer\s*\(([^)]+)\)/);
      if (producerMatch) {
        metadata.producer = this.decodePDFString(producerMatch[1]);
      }

      // Extract creation date
      const creationDateMatch = content.match(/\/CreationDate\s*\(([^)]+)\)/);
      if (creationDateMatch) {
        metadata.creationDate = this.parsePDFDate(creationDateMatch[1]);
      }

      // Estimate page count (basic approach)
      const pageCountMatch = content.match(/\/Count\s+(\d+)/);
      if (pageCountMatch) {
        metadata.pageCount = parseInt(pageCountMatch[1], 10);
      }

      return metadata;
    } catch (error) {
      console.warn('Failed to extract PDF metadata:', error);
      return {};
    }
  }

  private decodePDFString(pdfString: string): string {
    // Basic PDF string decoding (handles simple cases)
    return pdfString
      .replace(/\\n/g, '\n')
      .replace(/\\r/g, '\r')
      .replace(/\\t/g, '\t')
      .replace(/\\b/g, '\b')
      .replace(/\\f/g, '\f')
      .replace(/\\\(/g, '(')
      .replace(/\\\)/g, ')')
      .replace(/\\\\/g, '\\');
  }

  private parsePDFDate(pdfDate: string): string {
    // PDF date format: D:YYYYMMDDHHmmSSOHH'mm'
    const match = pdfDate.match(/D:(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/);
    if (match) {
      const [, year, month, day, hour, minute, second] = match;
      return new Date(
        parseInt(year, 10),
        parseInt(month, 10) - 1,
        parseInt(day, 10),
        parseInt(hour, 10),
        parseInt(minute, 10),
        parseInt(second, 10),
      ).toISOString();
    }
    return pdfDate;
  }

  private async extractContent(
    filePath: string,
    _options: DocumentParseOptions,
  ): Promise<DocumentContent> {
    try {
      // Note: This is a simplified implementation
      // In a real app, you would use a proper PDF text extraction library
      // like react-native-pdf-lib or integrate with a native PDF library

      const pages: DocumentPage[] = [];
      let fullText = '';

      // For now, we'll create a placeholder implementation
      // that indicates PDF text extraction is not yet fully implemented
      const placeholderText = `[PDF Content - Text extraction not yet implemented for: ${this.getFileNameWithoutExtension(
        filePath,
      )}]`;

      fullText = placeholderText;

      // Create a single page entry
      pages.push({
        pageNumber: 1,
        content: placeholderText,
        wordCount: this.countWords(placeholderText),
        characterCount: placeholderText.length,
      });

      return {
        text: fullText,
        pages,
      };
    } catch (error) {
      console.error('Failed to extract PDF content:', error);
      return {
        text: '',
        pages: [],
      };
    }
  }

  private countWords(text: string): number {
    return text
      .trim()
      .split(/\s+/)
      .filter(word => word.length > 0).length;
  }

  private generateDocumentId(
    filePath: string,
    modificationTime: string,
  ): string {
    const fileName = this.getFileNameWithoutExtension(filePath);
    const timestamp = new Date(modificationTime).getTime();
    return `pdf-${fileName}-${timestamp}`;
  }

  private getFileNameWithoutExtension(filePath: string): string {
    const fileName = filePath.split('/').pop() || filePath;
    const lastDotIndex = fileName.lastIndexOf('.');
    return lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName;
  }
}
