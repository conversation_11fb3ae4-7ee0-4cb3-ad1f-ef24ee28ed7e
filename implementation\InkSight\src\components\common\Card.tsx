/**
 * Material Design 3 Card Component
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import React from 'react';
import { View, ViewStyle } from 'react-native';
import { lightTheme, darkTheme } from '../../theme';

export type CardVariant = 'elevated' | 'filled' | 'outlined';

interface CardProps {
  children: React.ReactNode;
  variant?: CardVariant;
  isDark?: boolean;
  style?: ViewStyle;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'elevated',
  isDark = false,
  style,
}) => {
  const theme = isDark ? darkTheme : lightTheme;

  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: theme.shape.corner.medium,
      padding: theme.spacing.md,
    };

    const variantStyles: Record<CardVariant, ViewStyle> = {
      elevated: {
        backgroundColor: theme.colors.surface,
        ...theme.elevation.level1,
      },
      filled: {
        backgroundColor: theme.colors.surfaceVariant,
      },
      outlined: {
        backgroundColor: theme.colors.surface,
        borderWidth: 1,
        borderColor: theme.colors.outline,
      },
    };

    return {
      ...baseStyle,
      ...variantStyles[variant],
    };
  };

  return <View style={[getCardStyle(), style]}>{children}</View>;
};

export default Card;
