name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"
          cache-dependency-path: implementation/InkSight/package-lock.json

      - name: Install dependencies
        working-directory: implementation/InkSight
        run: npm ci

      - name: Run TypeScript type check
        working-directory: implementation/InkSight
        run: npm run type-check

      - name: Run tests
        working-directory: implementation/InkSight
        run: npm run test:coverage

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: implementation/InkSight/coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false

  lint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "npm"
          cache-dependency-path: implementation/InkSight/package-lock.json

      - name: Install dependencies
        working-directory: implementation/InkSight
        run: npm ci

      - name: Run ESLint
        working-directory: implementation/InkSight
        run: npm run lint

      - name: Check Prettier formatting
        working-directory: implementation/InkSight
        run: npm run format:check

  build-android:
    runs-on: ubuntu-latest
    needs: [test, lint]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "npm"
          cache-dependency-path: implementation/InkSight/package-lock.json

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: "17"

      - name: Setup Android SDK
        uses: android-actions/setup-android@v3

      - name: Install dependencies
        working-directory: implementation/InkSight
        run: npm ci

      - name: Build Android APK
        working-directory: implementation/InkSight
        run: |
          cd android
          ./gradlew assembleDebug

      - name: Upload Android APK
        uses: actions/upload-artifact@v4
        with:
          name: android-apk
          path: implementation/InkSight/android/app/build/outputs/apk/debug/app-debug.apk
