# InkSight Development Setup Guide

This guide will help you set up your development environment for InkSight.

## 📋 Prerequisites

### Required Software

1. **Node.js** (18.x or later)

   - Download from [nodejs.org](https://nodejs.org/)
   - Verify installation: `node --version`

2. **Git**

   - Download from [git-scm.com](https://git-scm.com/)
   - Verify installation: `git --version`

3. **React Native CLI**
   ```bash
   npm install -g @react-native-community/cli
   ```

### Platform-Specific Requirements

#### Android Development

1. **Android Studio**

   - Download from [developer.android.com](https://developer.android.com/studio)
   - Install Android SDK (API level 33 or higher)
   - Configure Android SDK path in environment variables

2. **Java Development Kit (JDK)**

   - Install JDK 17 (recommended)
   - Set JAVA_HOME environment variable

3. **Environment Variables**
   ```bash
   # Add to your shell profile (.bashrc, .zshrc, etc.)
   export ANDROID_HOME=$HOME/Android/Sdk
   export PATH=$PATH:$ANDROID_HOME/emulator
   export PATH=$PATH:$ANDROID_HOME/platform-tools
   ```

#### iOS Development (macOS only)

1. **Xcode**

   - Install from Mac App Store
   - Install Xcode Command Line Tools: `xcode-select --install`

2. **CocoaPods**
   ```bash
   sudo gem install cocoapods
   ```

## 🚀 Project Setup

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/InkSight.git
cd InkSight
```

### 2. Install Dependencies

```bash
cd implementation/InkSight
npm install
```

### 3. iOS Setup (macOS only)

```bash
cd ios
pod install
cd ..
```

### 4. Verify Installation

```bash
# Check React Native environment
npx react-native doctor

# Run linting
npm run lint

# Run type checking
npm run type-check

# Run tests
npm run test
```

## 🏃‍♂️ Running the Application

### Start Metro Bundler

```bash
npm start
```

### Run on Android

```bash
# Using npm script
npm run android

# Or directly with React Native CLI
npx react-native run-android
```

### Run on iOS (macOS only)

```bash
# Using npm script
npm run ios

# Or directly with React Native CLI
npx react-native run-ios
```

## 🛠️ Development Tools

### Code Quality Tools

The project includes several tools to maintain code quality:

- **ESLint**: JavaScript/TypeScript linting
- **Prettier**: Code formatting
- **Husky**: Git hooks for pre-commit checks
- **TypeScript**: Static type checking

### Available Scripts

```bash
# Development
npm start                 # Start Metro bundler
npm run android          # Run on Android
npm run ios              # Run on iOS

# Code Quality
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues automatically
npm run format           # Format code with Prettier
npm run format:check     # Check code formatting
npm run type-check       # Run TypeScript type checking

# Testing
npm run test             # Run Jest tests
npm run test:watch       # Run tests in watch mode
npm run test:coverage    # Run tests with coverage report
```

### IDE Configuration

#### Visual Studio Code (Recommended)

Install these extensions:

- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Prettier - Code formatter
- ESLint
- React Native Tools
- Auto Rename Tag
- Bracket Pair Colorizer

#### Settings

Add to your VS Code settings.json:

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

## 🐛 Troubleshooting

### Common Issues

#### Metro bundler issues

```bash
# Clear Metro cache
npx react-native start --reset-cache

# Clear npm cache
npm start -- --reset-cache
```

#### Android build issues

```bash
# Clean Android build
cd android
./gradlew clean
cd ..

# Reset Android project
npx react-native run-android --reset-cache
```

#### iOS build issues (macOS only)

```bash
# Clean iOS build
cd ios
xcodebuild clean
cd ..

# Reinstall pods
cd ios
pod deintegrate
pod install
cd ..
```

#### Node modules issues

```bash
# Clean install
rm -rf node_modules package-lock.json
npm install
```

### Environment Verification

Run this command to check your environment:

```bash
npx react-native doctor
```

This will verify:

- Node.js version
- Android SDK configuration
- iOS development tools (macOS only)
- Required dependencies

## 📱 Device Setup

### Android Device

1. Enable Developer Options:

   - Go to Settings > About Phone
   - Tap "Build Number" 7 times

2. Enable USB Debugging:

   - Go to Settings > Developer Options
   - Enable "USB Debugging"

3. Connect device and verify:
   ```bash
   adb devices
   ```

### iOS Device (macOS only)

1. Connect device to Mac
2. Trust the computer when prompted
3. Verify device is detected in Xcode

### Emulators

#### Android Emulator

1. Open Android Studio
2. Go to AVD Manager
3. Create a new virtual device
4. Choose API level 33 or higher
5. Start the emulator

#### iOS Simulator (macOS only)

```bash
# List available simulators
xcrun simctl list devices

# Start specific simulator
xcrun simctl boot "iPhone 14"
```

## 🔧 Advanced Configuration

### Environment Variables

Create a `.env` file in the project root:

```bash
# Development configuration
NODE_ENV=development
DEBUG=true

# Build configuration
ANDROID_KEYSTORE_PATH=
ANDROID_KEYSTORE_PASSWORD=
```

### Custom Metro Configuration

The project uses a custom Metro configuration for optimal bundling. See `metro.config.js` for details.

### TypeScript Configuration

TypeScript is configured with strict settings. See `tsconfig.json` for the complete configuration.

## 📚 Next Steps

After setting up your development environment:

1. Read the [Contributing Guidelines](../CONTRIBUTING.md)
2. Explore the [Architecture Documentation](architecture/)
3. Check out the [Feature Specifications](specifications/)
4. Review the [Testing Strategy](testing/Testing_Strategy_Framework.md)

## 🆘 Getting Help

If you encounter issues:

1. Check this troubleshooting section
2. Search existing GitHub issues
3. Ask in GitHub Discussions
4. Contact the development team

---

Happy coding! 🚀
