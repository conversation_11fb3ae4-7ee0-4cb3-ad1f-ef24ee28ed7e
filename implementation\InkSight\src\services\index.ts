/**
 * Services Initialization and Export
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import { Logger } from './Logger';
// import { ErrorHandler } from './ErrorHandler';
import { PerformanceMonitor } from './PerformanceMonitor';

// Initialize all services
export const initializeServices = async (): Promise<void> => {
  try {
    // Initialize logger first
    await Logger.initialize();
    Logger.info('Services initialization started', 'app');

    // Initialize performance monitoring
    PerformanceMonitor.initialize();
    Logger.info('Performance monitoring initialized', 'app');

    // Error handler is initialized automatically
    Logger.info('Error handler initialized', 'app');

    Logger.info('All services initialized successfully', 'app');
  } catch (error) {
    console.error('Failed to initialize services:', error);
    throw error;
  }
};

// Cleanup all services
export const cleanupServices = (): void => {
  try {
    Logger.info('Services cleanup started', 'app');

    PerformanceMonitor.destroy();
    Logger.info('Performance monitoring destroyed', 'app');

    Logger.info('Services cleanup completed', 'app');
  } catch (error) {
    console.error('Failed to cleanup services:', error);
  }
};

// Export services
export { Logger, log } from './Logger';
export {
  ErrorHandler,
  handleError,
  createError,
  AppError,
} from './ErrorHandler';
export {
  PerformanceMonitor,
  startTiming,
  endTiming,
  measureAsync,
  measure,
} from './PerformanceMonitor';

// Export types
export type { LogLevel, LogEntry, LoggerConfig } from './Logger';
export type {
  ErrorType,
  ErrorSeverity,
  ErrorContext,
  RecoveryStrategy,
  ErrorHandlerConfig,
} from './ErrorHandler';
export type {
  PerformanceMetric,
  MemoryInfo,
  RenderMetric,
  PerformanceThresholds,
  PerformanceConfig,
} from './PerformanceMonitor';
