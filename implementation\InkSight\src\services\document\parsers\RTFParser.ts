/**
 * RTF Parser Implementation
 * Handles Rich Text Format documents
 */

import RNFS from 'react-native-fs';
import {
  DocumentFormat,
  DocumentParser,
  DocumentParseResult,
  DocumentParseOptions,
  DocumentMetadata,
  DocumentContent,
  ParsedDocument,
  DocumentError,
  DocumentProcessingError,
} from '../../../types/document';

export class RTFParser implements DocumentParser {
  supportedFormats: DocumentFormat[] = [DocumentFormat.RTF];

  canParse(filePath: string, mimeType?: string): boolean {
    const extension = filePath.toLowerCase().split('.').pop();
    return (
      extension === 'rtf' ||
      mimeType === 'application/rtf' ||
      mimeType === 'text/rtf'
    );
  }

  async validateDocument(filePath: string): Promise<boolean> {
    try {
      // Check if file exists
      const exists = await RNFS.exists(filePath);
      if (!exists) {
        return false;
      }

      // Check RTF signature
      const header = await RNFS.read(filePath, 10, 0, 'utf8');
      return header.startsWith('{\\rtf');
    } catch (error) {
      console.error('RTF validation error:', error);
      return false;
    }
  }

  async parse(
    filePath: string,
    options: DocumentParseOptions = {
      extractText: true,
      extractMetadata: true,
      extractTableOfContents: false,
      generateThumbnail: false,
    },
  ): Promise<DocumentParseResult> {
    try {
      const metadata = await this.extractMetadataFromFile(filePath);
      let content: DocumentContent = { text: '' };

      // Extract content if requested
      if (options.extractText) {
        content = await this.extractContent(filePath, options);
      }

      const parsedDocument: ParsedDocument = {
        metadata,
        content,
      };

      return {
        success: true,
        document: parsedDocument,
      };
    } catch (error) {
      console.error('RTF parsing error:', error);
      return {
        success: false,
        error: `Failed to parse RTF: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      };
    }
  }

  async extractMetadata(filePath: string): Promise<DocumentMetadata> {
    try {
      return await this.extractMetadataFromFile(filePath);
    } catch (error) {
      throw new DocumentProcessingError(
        DocumentError.PARSING_ERROR,
        `Failed to extract RTF metadata: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        filePath,
        error instanceof Error ? error : undefined,
      );
    }
  }

  private async extractMetadataFromFile(
    filePath: string,
  ): Promise<DocumentMetadata> {
    const stats = await RNFS.stat(filePath);

    // Generate unique ID based on file path and modification time
    const id = this.generateDocumentId(filePath, stats.mtime.toString());

    // Extract RTF metadata from document info
    const rtfMetadata = await this.extractRTFMetadata(filePath);

    return {
      id,
      title: rtfMetadata.title || this.getFileNameWithoutExtension(filePath),
      author: rtfMetadata.author,
      publisher: rtfMetadata.company,
      description: rtfMetadata.subject,
      fileSize: stats.size,
      format: DocumentFormat.RTF,
      mimeType: 'application/rtf',
      createdAt: new Date(stats.ctime),
      modifiedAt: new Date(stats.mtime),
      readingProgress: 0,
      totalReadingTime: 0,
      wordCount: rtfMetadata.wordCount,
    };
  }

  private async extractRTFMetadata(filePath: string): Promise<{
    title?: string;
    author?: string;
    subject?: string;
    company?: string;
    wordCount?: number;
  }> {
    try {
      // Read the beginning of the RTF file to extract metadata
      const content = await RNFS.read(filePath, 8192, 0, 'utf8');
      const metadata: Record<string, unknown> = {};

      // Extract title
      const titleMatch = content.match(/\\title\s+([^}]+)}/);
      if (titleMatch) {
        metadata.title = this.cleanRTFText(titleMatch[1]);
      }

      // Extract author
      const authorMatch = content.match(/\\author\s+([^}]+)}/);
      if (authorMatch) {
        metadata.author = this.cleanRTFText(authorMatch[1]);
      }

      // Extract subject
      const subjectMatch = content.match(/\\subject\s+([^}]+)}/);
      if (subjectMatch) {
        metadata.subject = this.cleanRTFText(subjectMatch[1]);
      }

      // Extract company
      const companyMatch = content.match(/\\company\s+([^}]+)}/);
      if (companyMatch) {
        metadata.company = this.cleanRTFText(companyMatch[1]);
      }

      return metadata;
    } catch (error) {
      console.warn('Failed to extract RTF metadata:', error);
      return {};
    }
  }

  private async extractContent(
    filePath: string,
    options: DocumentParseOptions,
  ): Promise<DocumentContent> {
    try {
      const rtfContent = await RNFS.readFile(filePath, 'utf8');
      const plainText = this.rtfToPlainText(rtfContent);

      // Apply text length limit if specified
      let finalText = plainText;
      if (options.maxTextLength && plainText.length > options.maxTextLength) {
        finalText = plainText.substring(0, options.maxTextLength);
      }

      return {
        text: finalText,
        html: this.rtfToHTML(rtfContent),
      };
    } catch (error) {
      console.error('Failed to extract RTF content:', error);
      return {
        text: '',
      };
    }
  }

  private rtfToPlainText(rtfContent: string): string {
    try {
      // Remove RTF control words and groups
      let text = rtfContent;

      // Remove RTF header
      text = text.replace(/^{\\rtf[^}]*}/, '');

      // Remove control words (e.g., \par, \b, \i, etc.)
      text = text.replace(/\\[a-z]+\d*\s?/gi, '');

      // Remove control symbols
      text = text.replace(/\\[^a-z\s]/gi, '');

      // Remove braces
      text = text.replace(/[{}]/g, '');

      // Convert RTF line breaks
      text = text.replace(/\\par\s?/g, '\n');
      text = text.replace(/\\line\s?/g, '\n');

      // Clean up whitespace
      text = text.replace(/\s+/g, ' ').trim();

      return text;
    } catch (error) {
      console.error('RTF to plain text conversion failed:', error);
      return '';
    }
  }

  private rtfToHTML(rtfContent: string): string {
    try {
      // Basic RTF to HTML conversion
      let html = this.rtfToPlainText(rtfContent);

      // Convert line breaks to HTML
      html = html.replace(/\n/g, '<br>');

      // Wrap in basic HTML structure
      return `<div>${html}</div>`;
    } catch (error) {
      console.error('RTF to HTML conversion failed:', error);
      return '';
    }
  }

  private cleanRTFText(text: string): string {
    // Remove RTF control sequences from text
    return text
      .replace(/\\[a-z]+\d*\s?/gi, '')
      .replace(/\\[^a-z\s]/gi, '')
      .replace(/[{}]/g, '')
      .trim();
  }

  private generateDocumentId(
    filePath: string,
    modificationTime: string,
  ): string {
    const fileName = this.getFileNameWithoutExtension(filePath);
    const timestamp = new Date(modificationTime).getTime();
    return `rtf-${fileName}-${timestamp}`;
  }

  private getFileNameWithoutExtension(filePath: string): string {
    const fileName = filePath.split('/').pop() || filePath;
    const lastDotIndex = fileName.lastIndexOf('.');
    return lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName;
  }
}
