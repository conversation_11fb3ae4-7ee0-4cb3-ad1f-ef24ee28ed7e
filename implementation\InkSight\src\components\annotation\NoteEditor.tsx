/**
 * Note Editor Component
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import type { AppDispatch } from '../../store';
import { lightTheme, darkTheme } from '../../theme';
import {
  AnnotationType,
  NoteAnnotation,
  TextSelection,
} from '../../types/annotation';
import {
  createAnnotation,
  updateAnnotation,
  setActiveSelection,
  selectActiveSelection,
  // selectSelectedAnnotation,
} from '../../store/slices/annotationSlice';

interface NoteEditorProps {
  documentId: string;
  visible: boolean;
  onClose: () => void;
  isDark?: boolean;
  initialSelection?: TextSelection;
  existingNote?: NoteAnnotation;
}

const NoteEditor: React.FC<NoteEditorProps> = ({
  documentId,
  visible,
  onClose,
  isDark = false,
  initialSelection,
  existingNote,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const theme = isDark ? darkTheme : lightTheme;
  const activeSelection = useSelector(selectActiveSelection);
  // const selectedAnnotation = useSelector(selectSelectedAnnotation);

  // Form state
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [tags, setTags] = useState('');
  const [category, setCategory] = useState('');
  const [isPrivate, setIsPrivate] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Initialize form with existing note data
  useEffect(() => {
    if (existingNote) {
      setTitle(existingNote.title || '');
      setContent(existingNote.content || '');
      setTags(existingNote.tags?.join(', ') || '');
      setCategory(existingNote.category || '');
      setIsPrivate(existingNote.isPrivate || false);
    } else {
      // Reset form for new note
      setTitle('');
      setContent('');
      setTags('');
      setCategory('');
      setIsPrivate(false);
    }
  }, [existingNote, visible]);

  // Handle save note
  const handleSave = useCallback(async () => {
    if (!content.trim()) return;

    setIsSaving(true);

    try {
      const selection = initialSelection || activeSelection;
      if (!selection && !existingNote) {
        // Can't create note without selection
        return;
      }

      const noteData = {
        title: title.trim() || undefined,
        content: content.trim(),
        tags: tags.trim()
          ? tags
              .split(',')
              .map(tag => tag.trim())
              .filter(Boolean)
          : undefined,
        category: category.trim() || undefined,
        isPrivate,
      };

      if (existingNote) {
        // Update existing note
        await dispatch(
          updateAnnotation({
            id: existingNote.id,
            updates: noteData,
          }),
        );
      } else {
        // Create new note
        const noteAnnotation = {
          type: AnnotationType.NOTE,
          documentId,
          selection: selection!,
          position: {
            coordinates: {
              x: 0,
              y: 0,
              width: 0,
              height: 0,
            },
          },
          ...noteData,
        };

        await dispatch(createAnnotation(noteAnnotation));
        dispatch(setActiveSelection(null));
      }

      onClose();
    } catch (error) {
      console.error('Failed to save note:', error);
    } finally {
      setIsSaving(false);
    }
  }, [
    content,
    title,
    tags,
    category,
    isPrivate,
    initialSelection,
    activeSelection,
    existingNote,
    documentId,
    dispatch,
    onClose,
  ]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    if (!existingNote) {
      dispatch(setActiveSelection(null));
    }
    onClose();
  }, [existingNote, dispatch, onClose]);

  // Get selected text for display
  const getSelectedText = () => {
    const selection =
      initialSelection || activeSelection || existingNote?.selection;
    return selection?.selectedText || '';
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleCancel}
    >
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View
          style={[styles.header, { borderBottomColor: theme.colors.outline }]}
        >
          <TouchableOpacity onPress={handleCancel} style={styles.headerButton}>
            <Text
              style={[styles.headerButtonText, { color: theme.colors.primary }]}
            >
              Cancel
            </Text>
          </TouchableOpacity>

          <Text
            style={[styles.headerTitle, { color: theme.colors.onBackground }]}
          >
            {existingNote ? 'Edit Note' : 'New Note'}
          </Text>

          <TouchableOpacity
            onPress={handleSave}
            style={[
              styles.headerButton,
              (!content.trim() || isSaving) && styles.disabledButton,
            ]}
            disabled={!content.trim() || isSaving}
          >
            <Text
              style={[
                styles.headerButtonText,
                { color: theme.colors.primary },
                (!content.trim() || isSaving) && {
                  color: theme.colors.onSurfaceVariant,
                },
              ]}
            >
              {isSaving ? 'Saving...' : 'Save'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Selected text display */}
          {getSelectedText() && (
            <View
              style={[
                styles.selectedTextContainer,
                { backgroundColor: theme.colors.surfaceVariant },
              ]}
            >
              <Text
                style={[
                  styles.selectedTextLabel,
                  { color: theme.colors.onSurfaceVariant },
                ]}
              >
                Selected Text:
              </Text>
              <Text
                style={[styles.selectedText, { color: theme.colors.onSurface }]}
              >
                "{getSelectedText()}"
              </Text>
            </View>
          )}

          {/* Title input */}
          <View style={styles.inputGroup}>
            <Text
              style={[styles.inputLabel, { color: theme.colors.onBackground }]}
            >
              Title (Optional)
            </Text>
            <TextInput
              style={[
                styles.titleInput,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.outline,
                  color: theme.colors.onSurface,
                },
              ]}
              value={title}
              onChangeText={setTitle}
              placeholder="Enter note title..."
              placeholderTextColor={theme.colors.onSurfaceVariant}
              maxLength={100}
            />
          </View>

          {/* Content input */}
          <View style={styles.inputGroup}>
            <Text
              style={[styles.inputLabel, { color: theme.colors.onBackground }]}
            >
              Note Content *
            </Text>
            <TextInput
              style={[
                styles.contentInput,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.outline,
                  color: theme.colors.onSurface,
                },
              ]}
              value={content}
              onChangeText={setContent}
              placeholder="Write your note here..."
              placeholderTextColor={theme.colors.onSurfaceVariant}
              multiline
              textAlignVertical="top"
              maxLength={5000}
            />
            <Text
              style={[
                styles.characterCount,
                { color: theme.colors.onSurfaceVariant },
              ]}
            >
              {content.length}/5000
            </Text>
          </View>

          {/* Tags input */}
          <View style={styles.inputGroup}>
            <Text
              style={[styles.inputLabel, { color: theme.colors.onBackground }]}
            >
              Tags (Optional)
            </Text>
            <TextInput
              style={[
                styles.titleInput,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.outline,
                  color: theme.colors.onSurface,
                },
              ]}
              value={tags}
              onChangeText={setTags}
              placeholder="Enter tags separated by commas..."
              placeholderTextColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Category input */}
          <View style={styles.inputGroup}>
            <Text
              style={[styles.inputLabel, { color: theme.colors.onBackground }]}
            >
              Category (Optional)
            </Text>
            <TextInput
              style={[
                styles.titleInput,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.outline,
                  color: theme.colors.onSurface,
                },
              ]}
              value={category}
              onChangeText={setCategory}
              placeholder="Enter category..."
              placeholderTextColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Privacy toggle */}
          <View style={styles.inputGroup}>
            <TouchableOpacity
              style={styles.privacyToggle}
              onPress={() => setIsPrivate(!isPrivate)}
              activeOpacity={0.7}
            >
              <View
                style={[
                  styles.checkbox,
                  {
                    borderColor: theme.colors.outline,
                    backgroundColor: isPrivate
                      ? theme.colors.primary
                      : 'transparent',
                  },
                ]}
              >
                {isPrivate && (
                  <Text
                    style={[
                      styles.checkmark,
                      { color: theme.colors.onPrimary },
                    ]}
                  >
                    ✓
                  </Text>
                )}
              </View>
              <Text
                style={[
                  styles.privacyLabel,
                  { color: theme.colors.onBackground },
                ]}
              >
                Private Note
              </Text>
            </TouchableOpacity>
            <Text
              style={[
                styles.privacyDescription,
                { color: theme.colors.onSurfaceVariant },
              ]}
            >
              Private notes are only visible to you
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  headerButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.5,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  selectedTextContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  selectedTextLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  selectedText: {
    fontSize: 14,
    fontStyle: 'italic',
    lineHeight: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  titleInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  contentInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    minHeight: 120,
  },
  characterCount: {
    fontSize: 12,
    textAlign: 'right',
    marginTop: 4,
  },
  privacyToggle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderRadius: 4,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkmark: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  privacyLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  privacyDescription: {
    fontSize: 12,
    marginTop: 4,
    marginLeft: 32,
  },
});

export default NoteEditor;
