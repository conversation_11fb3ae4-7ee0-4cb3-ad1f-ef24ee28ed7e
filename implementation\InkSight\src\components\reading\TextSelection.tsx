/**
 * Text Selection Component
 * Handles text selection, highlighting, and annotation functionality
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  TextInput,
  Alert,
  Clipboard,
} from 'react-native';
import { TextSelectionProps } from '../../types/reading';

const HIGHLIGHT_COLORS = [
  { name: 'Yellow', color: '#FFEB3B' },
  { name: 'Green', color: '#4CAF50' },
  { name: 'Blue', color: '#2196F3' },
  { name: 'Orange', color: '#FF9800' },
  { name: 'Pink', color: '#E91E63' },
  { name: 'Purple', color: '#9C27B0' },
];

export const TextSelectionComponent: React.FC<TextSelectionProps> = ({
  selection,
  onSelectionChange,
  onCopy,
  onHighlight,
  onNote,
  disabled = false,
}) => {
  const [showActions, setShowActions] = useState(false);
  const [showNoteModal, setShowNoteModal] = useState(false);
  const [noteText, setNoteText] = useState('');
  const [showColorPicker, setShowColorPicker] = useState(false);

  const handleCopy = async () => {
    if (!selection || disabled) return;

    try {
      await Clipboard.setString(selection.selectedText);
      onCopy?.(selection.selectedText);
      Alert.alert('Copied', 'Text copied to clipboard');
      setShowActions(false);
    } catch {
      Alert.alert('Error', 'Failed to copy text');
    }
  };

  const handleHighlight = (color: string) => {
    if (!selection || disabled) return;

    onHighlight?.(selection, color);
    setShowColorPicker(false);
    setShowActions(false);
  };

  const handleAddNote = () => {
    if (!selection || disabled) return;

    if (noteText.trim()) {
      onNote?.(selection, noteText.trim());
      setNoteText('');
      setShowNoteModal(false);
      setShowActions(false);
    } else {
      Alert.alert('Error', 'Please enter a note');
    }
  };

  const handleClearSelection = () => {
    onSelectionChange(undefined);
    setShowActions(false);
  };

  const openActions = () => {
    if (selection && !disabled) {
      setShowActions(true);
    }
  };

  const openNoteModal = () => {
    setShowActions(false);
    setShowNoteModal(true);
  };

  const openColorPicker = () => {
    setShowActions(false);
    setShowColorPicker(true);
  };

  if (!selection) {
    return null;
  }

  return (
    <>
      {/* Selection Indicator */}
      <TouchableOpacity
        style={styles.selectionIndicator}
        onPress={openActions}
        disabled={disabled}
      >
        <Text style={styles.selectionText} numberOfLines={2}>
          "{selection.selectedText}"
        </Text>
        <Text style={styles.selectionInfo}>Tap to see options</Text>
      </TouchableOpacity>

      {/* Actions Modal */}
      <Modal
        visible={showActions}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowActions(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.actionsContainer}>
            <Text style={styles.actionsTitle}>Text Actions</Text>

            <Text style={styles.selectedText} numberOfLines={3}>
              "{selection.selectedText}"
            </Text>

            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleCopy}
              >
                <Text style={styles.actionButtonText}>Copy</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={openColorPicker}
              >
                <Text style={styles.actionButtonText}>Highlight</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={openNoteModal}
              >
                <Text style={styles.actionButtonText}>Add Note</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.clearButton]}
                onPress={handleClearSelection}
              >
                <Text style={[styles.actionButtonText, styles.clearButtonText]}>
                  Clear
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Color Picker Modal */}
      <Modal
        visible={showColorPicker}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowColorPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.colorPickerContainer}>
            <Text style={styles.colorPickerTitle}>Choose Highlight Color</Text>

            <View style={styles.colorGrid}>
              {HIGHLIGHT_COLORS.map(colorOption => (
                <TouchableOpacity
                  key={colorOption.name}
                  style={[
                    styles.colorOption,
                    { backgroundColor: colorOption.color },
                  ]}
                  onPress={() => handleHighlight(colorOption.color)}
                >
                  <Text style={styles.colorName}>{colorOption.name}</Text>
                </TouchableOpacity>
              ))}
            </View>

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setShowColorPicker(false)}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Note Modal */}
      <Modal
        visible={showNoteModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowNoteModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.noteModalContainer}>
            <Text style={styles.noteModalTitle}>Add Note</Text>

            <Text style={styles.selectedText} numberOfLines={2}>
              "{selection.selectedText}"
            </Text>

            <TextInput
              style={styles.noteInput}
              value={noteText}
              onChangeText={setNoteText}
              placeholder="Enter your note..."
              multiline={true}
              numberOfLines={4}
              textAlignVertical="top"
              autoFocus={true}
            />

            <View style={styles.noteModalButtons}>
              <TouchableOpacity
                style={styles.noteModalButton}
                onPress={() => setShowNoteModal(false)}
              >
                <Text style={styles.noteModalButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.noteModalButton, styles.saveButton]}
                onPress={handleAddNote}
              >
                <Text
                  style={[styles.noteModalButtonText, styles.saveButtonText]}
                >
                  Save Note
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  selectionIndicator: {
    position: 'absolute',
    bottom: 100,
    left: 16,
    right: 16,
    backgroundColor: '#6200EE',
    borderRadius: 8,
    padding: 12,
    elevation: 4,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  selectionText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  selectionInfo: {
    color: '#E1BEE7',
    fontSize: 12,
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    minWidth: 280,
    maxWidth: 320,
    elevation: 8,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  actionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 12,
  },
  selectedText: {
    fontSize: 14,
    color: '#666666',
    fontStyle: 'italic',
    textAlign: 'center',
    marginBottom: 16,
    paddingHorizontal: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    backgroundColor: '#6200EE',
    borderRadius: 6,
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginBottom: 8,
    minWidth: '48%',
  },
  clearButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  clearButtonText: {
    color: '#FFFFFF',
  },
  colorPickerContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    minWidth: 280,
    elevation: 8,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  colorPickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 16,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  colorOption: {
    width: '30%',
    aspectRatio: 1,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  colorName: {
    fontSize: 12,
    fontWeight: '500',
    color: '#000000',
    textAlign: 'center',
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 6,
    paddingVertical: 12,
  },
  cancelButtonText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  noteModalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    minWidth: 300,
    maxWidth: 350,
    elevation: 8,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  noteModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 12,
  },
  noteInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    minHeight: 100,
    marginBottom: 16,
  },
  noteModalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  noteModalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 6,
    marginHorizontal: 4,
    backgroundColor: '#F5F5F5',
  },
  saveButton: {
    backgroundColor: '#6200EE',
  },
  noteModalButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666666',
    textAlign: 'center',
  },
  saveButtonText: {
    color: '#FFFFFF',
  },
});
