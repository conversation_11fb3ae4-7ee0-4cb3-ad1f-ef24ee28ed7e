/**
 * Reading interface types for InkSight
 * Defines types for document viewing, navigation, and reading state management
 */

import { DocumentMetadata, DocumentContent } from './document';

export interface ReadingPosition {
  documentId: string;
  pageNumber?: number;
  chapterId?: string;
  scrollPosition: number;
  characterOffset: number;
  timestamp: Date;
}

export interface ReadingProgress {
  documentId: string;
  currentPosition: ReadingPosition;
  totalPages?: number;
  totalCharacters: number;
  readCharacters: number;
  progressPercentage: number;
  readingTime: number; // in seconds
  lastReadAt: Date;
}

export interface ZoomState {
  level: number; // 0.5 to 5.0
  centerX: number;
  centerY: number;
  fitMode: 'width' | 'height' | 'page' | 'custom';
}

export interface TextSelection {
  documentId: string;
  startPosition: {
    pageNumber?: number;
    chapterId?: string;
    characterOffset: number;
  };
  endPosition: {
    pageNumber?: number;
    chapterId?: string;
    characterOffset: number;
  };
  selectedText: string;
  timestamp: Date;
}

export interface NavigationState {
  currentPage: number;
  totalPages: number;
  currentChapter?: string;
  canGoNext: boolean;
  canGoPrevious: boolean;
}

export interface DocumentViewerState {
  documentId: string;
  metadata: DocumentMetadata;
  content: DocumentContent;
  isLoading: boolean;
  error?: string;
  readingProgress: ReadingProgress;
  zoomState: ZoomState;
  navigationState: NavigationState;
  textSelection?: TextSelection;
  isFullscreen: boolean;
}

export interface ReadingSettings {
  fontSize: number;
  fontFamily: string;
  lineHeight: number;
  backgroundColor: string;
  textColor: string;
  theme: 'light' | 'dark' | 'sepia';
  autoScroll: boolean;
  autoScrollSpeed: number;
  pageTransition: 'slide' | 'fade' | 'none';
}

export interface DocumentViewerProps {
  documentId: string;
  onLoadingChange?: (isLoading: boolean) => void;
  onError?: (error: string) => void;
  onProgressChange?: (progress: ReadingProgress) => void;
  onNavigationChange?: (navigation: NavigationState) => void;
  settings?: Partial<ReadingSettings>;
}

export interface NavigationControlsProps {
  navigationState: NavigationState;
  onPageChange: (page: number) => void;
  onChapterChange?: (chapterId: string) => void;
  onGoToPage?: (page: number) => void;
  disabled?: boolean;
}

export interface ZoomControlsProps {
  zoomState: ZoomState;
  onZoomChange: (zoom: ZoomState) => void;
  onFitToWidth: () => void;
  onFitToHeight: () => void;
  onFitToPage: () => void;
  onResetZoom: () => void;
  disabled?: boolean;
}

export interface ProgressTrackerProps {
  progress: ReadingProgress;
  onPositionChange?: (position: ReadingPosition) => void;
  showDetails?: boolean;
  compact?: boolean;
}

export interface TextSelectionProps {
  selection?: TextSelection;
  onSelectionChange: (selection: TextSelection | undefined) => void;
  onCopy?: (text: string) => void;
  onHighlight?: (selection: TextSelection, color: string) => void;
  onNote?: (selection: TextSelection, note: string) => void;
  disabled?: boolean;
}

// Reading service interfaces
export interface ReadingService {
  loadDocument(documentId: string): Promise<DocumentViewerState>;
  updateReadingProgress(progress: ReadingProgress): Promise<void>;
  saveReadingPosition(position: ReadingPosition): Promise<void>;
  getReadingHistory(documentId: string): Promise<ReadingProgress[]>;
  updateSettings(settings: Partial<ReadingSettings>): Promise<void>;
  getSettings(): Promise<ReadingSettings>;
}

// Events
export interface ReadingEvent {
  type: 'page_change' | 'zoom_change' | 'selection_change' | 'progress_update';
  documentId: string;
  timestamp: Date;
  data: unknown;
}

export interface PageChangeEvent extends ReadingEvent {
  type: 'page_change';
  data: {
    previousPage: number;
    currentPage: number;
    totalPages: number;
  };
}

export interface ZoomChangeEvent extends ReadingEvent {
  type: 'zoom_change';
  data: {
    previousZoom: ZoomState;
    currentZoom: ZoomState;
  };
}

export interface SelectionChangeEvent extends ReadingEvent {
  type: 'selection_change';
  data: {
    selection?: TextSelection;
  };
}

export interface ProgressUpdateEvent extends ReadingEvent {
  type: 'progress_update';
  data: {
    progress: ReadingProgress;
  };
}

// Error types
export enum ReadingError {
  DOCUMENT_NOT_FOUND = 'DOCUMENT_NOT_FOUND',
  DOCUMENT_LOAD_FAILED = 'DOCUMENT_LOAD_FAILED',
  INVALID_PAGE = 'INVALID_PAGE',
  ZOOM_FAILED = 'ZOOM_FAILED',
  SELECTION_FAILED = 'SELECTION_FAILED',
  PROGRESS_SAVE_FAILED = 'PROGRESS_SAVE_FAILED',
}

export class ReadingException extends Error {
  constructor(
    public errorType: ReadingError,
    message: string,
    public documentId?: string,
    public originalError?: Error,
  ) {
    super(message);
    this.name = 'ReadingException';
  }
}
