/**
 * Annotation Redux Slice
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  Annotation,
  HighlightColor,
  AnnotationSearchCriteria,
  AnnotationSearchResult,
  AnnotationStatistics,
  AnnotationExportOptions,
  AnnotationImportResult,
  TextSelection,
} from '../../types/annotation';

// State interface
export interface AnnotationState {
  // Current document annotations
  currentDocumentId: string | null;
  annotations: Record<string, Annotation>; // id -> annotation
  documentAnnotations: Record<string, string[]>; // documentId -> annotationIds

  // UI state
  selectedAnnotationId: string | null;
  isCreatingAnnotation: boolean;
  activeSelection: TextSelection | null;
  highlightColor: HighlightColor;
  showAnnotationPanel: boolean;

  // Search and filtering
  searchCriteria: AnnotationSearchCriteria | null;
  searchResults: AnnotationSearchResult | null;
  isSearching: boolean;

  // Statistics
  statistics: AnnotationStatistics | null;

  // Export/Import
  isExporting: boolean;
  isImporting: boolean;
  lastExportPath: string | null;
  lastImportResult: AnnotationImportResult | null;

  // Loading states
  isLoading: boolean;
  isSaving: boolean;
  isDeleting: boolean;

  // Error handling
  error: string | null;
  lastError: string | null;
}

// Initial state
const initialState: AnnotationState = {
  currentDocumentId: null,
  annotations: {},
  documentAnnotations: {},

  selectedAnnotationId: null,
  isCreatingAnnotation: false,
  activeSelection: null,
  highlightColor: HighlightColor.YELLOW,
  showAnnotationPanel: false,

  searchCriteria: null,
  searchResults: null,
  isSearching: false,

  statistics: null,

  isExporting: false,
  isImporting: false,
  lastExportPath: null,
  lastImportResult: null,

  isLoading: false,
  isSaving: false,
  isDeleting: false,

  error: null,
  lastError: null,
};

// Async thunks (will be implemented with actual service calls)
export const loadAnnotationsForDocument = createAsyncThunk(
  'annotation/loadAnnotationsForDocument',
  async (documentId: string) => {
    // TODO: Implement with AnnotationService
    // const annotations = await AnnotationService.getAnnotationsForDocument(documentId);
    // return { documentId, annotations };
    return { documentId, annotations: [] as Annotation[] };
  },
);

export const createAnnotation = createAsyncThunk(
  'annotation/createAnnotation',
  async (
    annotationData: Omit<Annotation, 'id' | 'createdAt' | 'updatedAt'>,
  ) => {
    // TODO: Implement with AnnotationService
    // const annotation = await AnnotationService.createAnnotation(annotationData);
    // return annotation;
    const annotation: Annotation = {
      ...annotationData,
      id: `annotation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    } as Annotation;
    return annotation;
  },
);

export const updateAnnotation = createAsyncThunk(
  'annotation/updateAnnotation',
  async ({ id, updates }: { id: string; updates: Partial<Annotation> }) => {
    // TODO: Implement with AnnotationService
    // const annotation = await AnnotationService.updateAnnotation(id, updates);
    // return annotation;
    return { id, updates };
  },
);

export const deleteAnnotation = createAsyncThunk(
  'annotation/deleteAnnotation',
  async (id: string) => {
    // TODO: Implement with AnnotationService
    // await AnnotationService.deleteAnnotation(id);
    // return id;
    return id;
  },
);

export const searchAnnotations = createAsyncThunk(
  'annotation/searchAnnotations',
  async (_criteria: AnnotationSearchCriteria) => {
    // TODO: Implement with AnnotationService
    // const results = await AnnotationService.searchAnnotations(criteria);
    // return results;
    return {
      annotations: [],
      totalCount: 0,
      hasMore: false,
    } as AnnotationSearchResult;
  },
);

export const exportAnnotations = createAsyncThunk(
  'annotation/exportAnnotations',
  async ({
    documentId: _documentId,
    options: _options,
  }: {
    documentId: string;
    options: AnnotationExportOptions;
  }) => {
    // TODO: Implement with AnnotationService
    // const exportPath = await AnnotationService.exportAnnotations(documentId, options);
    // return exportPath;
    return '/path/to/exported/annotations';
  },
);

// Annotation slice
const annotationSlice = createSlice({
  name: 'annotation',
  initialState,
  reducers: {
    // UI state management
    setCurrentDocument: (state, action: PayloadAction<string>) => {
      state.currentDocumentId = action.payload;
      state.selectedAnnotationId = null;
      state.activeSelection = null;
    },

    setSelectedAnnotation: (state, action: PayloadAction<string | null>) => {
      state.selectedAnnotationId = action.payload;
    },

    setActiveSelection: (
      state,
      action: PayloadAction<TextSelection | null>,
    ) => {
      state.activeSelection = action.payload;
      if (action.payload) {
        state.isCreatingAnnotation = true;
      }
    },

    setHighlightColor: (state, action: PayloadAction<HighlightColor>) => {
      state.highlightColor = action.payload;
    },

    setShowAnnotationPanel: (state, action: PayloadAction<boolean>) => {
      state.showAnnotationPanel = action.payload;
    },

    startCreatingAnnotation: state => {
      state.isCreatingAnnotation = true;
    },

    cancelCreatingAnnotation: state => {
      state.isCreatingAnnotation = false;
      state.activeSelection = null;
    },

    // Search management
    setSearchCriteria: (
      state,
      action: PayloadAction<AnnotationSearchCriteria | null>,
    ) => {
      state.searchCriteria = action.payload;
    },

    clearSearchResults: state => {
      state.searchResults = null;
      state.searchCriteria = null;
    },

    // Error handling
    clearError: state => {
      state.error = null;
    },

    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.lastError = action.payload;
    },

    // Reset state
    resetAnnotationState: state => {
      Object.assign(state, initialState);
    },
  },
  extraReducers: builder => {
    // Load annotations for document
    builder
      .addCase(loadAnnotationsForDocument.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadAnnotationsForDocument.fulfilled, (state, action) => {
        state.isLoading = false;
        const { documentId, annotations } = action.payload;

        // Clear existing annotations for this document
        const existingIds = state.documentAnnotations[documentId] || [];
        existingIds.forEach(id => {
          delete state.annotations[id];
        });

        // Add new annotations
        const annotationIds: string[] = [];
        annotations.forEach(annotation => {
          state.annotations[annotation.id] = annotation;
          annotationIds.push(annotation.id);
        });

        state.documentAnnotations[documentId] = annotationIds;
      })
      .addCase(loadAnnotationsForDocument.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to load annotations';
      });

    // Create annotation
    builder
      .addCase(createAnnotation.pending, state => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(createAnnotation.fulfilled, (state, action) => {
        state.isSaving = false;
        state.isCreatingAnnotation = false;
        state.activeSelection = null;

        const annotation = action.payload;
        state.annotations[annotation.id] = annotation;

        // Add to document annotations
        const documentId = annotation.documentId;
        if (!state.documentAnnotations[documentId]) {
          state.documentAnnotations[documentId] = [];
        }
        state.documentAnnotations[documentId].push(annotation.id);
      })
      .addCase(createAnnotation.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.error.message || 'Failed to create annotation';
      });

    // Update annotation
    builder
      .addCase(updateAnnotation.pending, state => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(updateAnnotation.fulfilled, (state, action) => {
        state.isSaving = false;
        const { id, updates } = action.payload;

        if (state.annotations[id]) {
          const existingAnnotation = state.annotations[id];
          state.annotations[id] = {
            ...existingAnnotation,
            ...updates,
            updatedAt: new Date().toISOString(),
          } as Annotation;
        }
      })
      .addCase(updateAnnotation.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.error.message || 'Failed to update annotation';
      });

    // Delete annotation
    builder
      .addCase(deleteAnnotation.pending, state => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(deleteAnnotation.fulfilled, (state, action) => {
        state.isDeleting = false;
        const id = action.payload;

        // Remove from annotations
        const annotation = state.annotations[id];
        if (annotation) {
          delete state.annotations[id];

          // Remove from document annotations
          const documentId = annotation.documentId;
          if (state.documentAnnotations[documentId]) {
            state.documentAnnotations[documentId] = state.documentAnnotations[
              documentId
            ].filter(annotationId => annotationId !== id);
          }

          // Clear selection if this annotation was selected
          if (state.selectedAnnotationId === id) {
            state.selectedAnnotationId = null;
          }
        }
      })
      .addCase(deleteAnnotation.rejected, (state, action) => {
        state.isDeleting = false;
        state.error = action.error.message || 'Failed to delete annotation';
      });

    // Search annotations
    builder
      .addCase(searchAnnotations.pending, state => {
        state.isSearching = true;
        state.error = null;
      })
      .addCase(searchAnnotations.fulfilled, (state, action) => {
        state.isSearching = false;
        state.searchResults = action.payload;
      })
      .addCase(searchAnnotations.rejected, (state, action) => {
        state.isSearching = false;
        state.error = action.error.message || 'Failed to search annotations';
      });

    // Export annotations
    builder
      .addCase(exportAnnotations.pending, state => {
        state.isExporting = true;
        state.error = null;
      })
      .addCase(exportAnnotations.fulfilled, (state, action) => {
        state.isExporting = false;
        state.lastExportPath = action.payload;
      })
      .addCase(exportAnnotations.rejected, (state, action) => {
        state.isExporting = false;
        state.error = action.error.message || 'Failed to export annotations';
      });
  },
});

// Export actions
export const {
  setCurrentDocument,
  setSelectedAnnotation,
  setActiveSelection,
  setHighlightColor,
  setShowAnnotationPanel,
  startCreatingAnnotation,
  cancelCreatingAnnotation,
  setSearchCriteria,
  clearSearchResults,
  clearError,
  setError,
  resetAnnotationState,
} = annotationSlice.actions;

// Selectors
export const selectCurrentDocumentId = (state: {
  annotation: AnnotationState;
}) => state.annotation.currentDocumentId;

export const selectAnnotationsForCurrentDocument = (state: {
  annotation: AnnotationState;
}) => {
  const { currentDocumentId, annotations, documentAnnotations } =
    state.annotation;
  if (!currentDocumentId) return [];

  const annotationIds = documentAnnotations[currentDocumentId] || [];
  return annotationIds.map(id => annotations[id]).filter(Boolean);
};

export const selectSelectedAnnotation = (state: {
  annotation: AnnotationState;
}) => {
  const { selectedAnnotationId, annotations } = state.annotation;
  return selectedAnnotationId ? annotations[selectedAnnotationId] : null;
};

export const selectActiveSelection = (state: { annotation: AnnotationState }) =>
  state.annotation.activeSelection;

export const selectHighlightColor = (state: { annotation: AnnotationState }) =>
  state.annotation.highlightColor;

export const selectIsCreatingAnnotation = (state: {
  annotation: AnnotationState;
}) => state.annotation.isCreatingAnnotation;

export const selectShowAnnotationPanel = (state: {
  annotation: AnnotationState;
}) => state.annotation.showAnnotationPanel;

export const selectSearchResults = (state: { annotation: AnnotationState }) =>
  state.annotation.searchResults;

export const selectIsLoading = (state: { annotation: AnnotationState }) =>
  state.annotation.isLoading;

export const selectError = (state: { annotation: AnnotationState }) =>
  state.annotation.error;

// Export reducer
export default annotationSlice.reducer;
