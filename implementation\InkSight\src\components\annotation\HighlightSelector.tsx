/**
 * Highlight Selector Component
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  PanResponder,
  GestureResponderEvent,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import type { AppDispatch } from '../../store';
import { lightTheme, darkTheme } from '../../theme';
import {
  HighlightColor,
  AnnotationType,
  TextSelection,
  HighlightAnnotation,
} from '../../types/annotation';
import {
  setActiveSelection,
  setHighlightColor,
  createAnnotation,
  selectHighlightColor,
  selectActiveSelection,
  // selectIsCreatingAnnotation,
} from '../../store/slices/annotationSlice';

interface HighlightSelectorProps {
  documentId: string;
  isDark?: boolean;
  onSelectionStart?: () => void;
  onSelectionEnd?: (selection: TextSelection) => void;
  onHighlightCreated?: (highlight: HighlightAnnotation) => void;
}

const HighlightSelector: React.FC<HighlightSelectorProps> = ({
  documentId,
  isDark = false,
  onSelectionStart,
  onSelectionEnd,
  onHighlightCreated: _onHighlightCreated,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const theme = isDark ? darkTheme : lightTheme;
  const highlightColor = useSelector(selectHighlightColor);
  const activeSelection = useSelector(selectActiveSelection);
  // const _isCreatingAnnotation = useSelector(selectIsCreatingAnnotation);

  const [isSelecting, setIsSelecting] = useState(false);
  const [selectionStart, setSelectionStart] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [selectionEnd, setSelectionEnd] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [showColorPicker, setShowColorPicker] = useState(false);

  // Available highlight colors
  const highlightColors = Object.values(HighlightColor);

  // Handle text selection start
  const handleSelectionStart = useCallback(
    (event: GestureResponderEvent) => {
      const { pageX, pageY } = event.nativeEvent;
      setIsSelecting(true);
      setSelectionStart({ x: pageX, y: pageY });
      setSelectionEnd(null);
      onSelectionStart?.();
    },
    [onSelectionStart],
  );

  // Handle text selection move
  const handleSelectionMove = useCallback(
    (event: GestureResponderEvent) => {
      if (!isSelecting) return;
      const { pageX, pageY } = event.nativeEvent;
      setSelectionEnd({ x: pageX, y: pageY });
    },
    [isSelecting],
  );

  // Handle text selection end
  const handleSelectionEnd = useCallback(() => {
    if (!isSelecting || !selectionStart || !selectionEnd) {
      setIsSelecting(false);
      return;
    }

    setIsSelecting(false);

    // Create text selection object
    // In a real implementation, this would extract the actual selected text
    // from the document viewer component
    const selection: TextSelection = {
      startOffset: Math.min(selectionStart.x, selectionEnd.x),
      endOffset: Math.max(selectionStart.x, selectionEnd.x),
      selectedText: 'Selected text placeholder', // Would be extracted from document
      rangeData: JSON.stringify({
        start: selectionStart,
        end: selectionEnd,
      }),
    };

    dispatch(setActiveSelection(selection));
    onSelectionEnd?.(selection);
    setShowColorPicker(true);
  }, [isSelecting, selectionStart, selectionEnd, dispatch, onSelectionEnd]);

  // Pan responder for text selection
  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderGrant: handleSelectionStart,
    onPanResponderMove: handleSelectionMove,
    onPanResponderRelease: handleSelectionEnd,
  });

  // Handle color selection
  const handleColorSelect = useCallback(
    (color: HighlightColor) => {
      dispatch(setHighlightColor(color));
      setShowColorPicker(false);

      if (activeSelection) {
        // Create highlight annotation
        const highlightAnnotation = {
          type: AnnotationType.HIGHLIGHT,
          documentId,
          selection: activeSelection,
          color,
          opacity: 0.3,
          position: {
            coordinates: {
              x: selectionStart?.x || 0,
              y: selectionStart?.y || 0,
              width: Math.abs(
                (selectionEnd?.x || 0) - (selectionStart?.x || 0),
              ),
              height: Math.abs(
                (selectionEnd?.y || 0) - (selectionStart?.y || 0),
              ),
            },
          },
        };

        dispatch(createAnnotation(highlightAnnotation));
        dispatch(setActiveSelection(null));

        // Reset selection state
        setSelectionStart(null);
        setSelectionEnd(null);
      }
    },
    [dispatch, documentId, activeSelection, selectionStart, selectionEnd],
  );

  // Handle cancel selection
  const handleCancelSelection = useCallback(() => {
    setShowColorPicker(false);
    dispatch(setActiveSelection(null));
    setSelectionStart(null);
    setSelectionEnd(null);
  }, [dispatch]);

  // Render selection overlay
  const renderSelectionOverlay = () => {
    if (!isSelecting || !selectionStart || !selectionEnd) return null;

    const left = Math.min(selectionStart.x, selectionEnd.x);
    const top = Math.min(selectionStart.y, selectionEnd.y);
    const width = Math.abs(selectionEnd.x - selectionStart.x);
    const height = Math.abs(selectionEnd.y - selectionStart.y);

    return (
      <View
        style={[
          styles.selectionOverlay,
          {
            left,
            top,
            width,
            height,
            backgroundColor: highlightColor,
          },
        ]}
        pointerEvents="none"
      />
    );
  };

  // Render color picker modal
  const renderColorPicker = () => (
    <Modal
      visible={showColorPicker}
      transparent
      animationType="fade"
      onRequestClose={handleCancelSelection}
    >
      <View style={styles.modalOverlay}>
        <View
          style={[
            styles.colorPickerContainer,
            { backgroundColor: theme.colors.surface },
          ]}
        >
          <Text
            style={[styles.colorPickerTitle, { color: theme.colors.onSurface }]}
          >
            Choose Highlight Color
          </Text>

          <View style={styles.colorGrid}>
            {highlightColors.map(color => (
              <TouchableOpacity
                key={color}
                style={[
                  styles.colorOption,
                  { backgroundColor: color },
                  highlightColor === color && styles.selectedColor,
                ]}
                onPress={() => handleColorSelect(color)}
                activeOpacity={0.7}
              />
            ))}
          </View>

          <View style={styles.colorPickerActions}>
            <TouchableOpacity
              style={[
                styles.cancelButton,
                { borderColor: theme.colors.outline },
              ]}
              onPress={handleCancelSelection}
            >
              <Text
                style={[
                  styles.cancelButtonText,
                  { color: theme.colors.onSurface },
                ]}
              >
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container} {...panResponder.panHandlers}>
      {renderSelectionOverlay()}
      {renderColorPicker()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  selectionOverlay: {
    position: 'absolute',
    opacity: 0.3,
    borderRadius: 2,
    pointerEvents: 'none',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  colorPickerContainer: {
    margin: 20,
    padding: 20,
    borderRadius: 12,
    minWidth: 280,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  colorPickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 12,
    marginBottom: 20,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedColor: {
    borderColor: '#000',
    borderWidth: 3,
  },
  colorPickerActions: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  cancelButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default HighlightSelector;
