/**
 * Color Picker Component
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { lightTheme, darkTheme } from '../../theme';
import { HighlightColor } from '../../types/annotation';

interface ColorPickerProps {
  selectedColor: HighlightColor;
  onColorSelect: (color: HighlightColor) => void;
  isDark?: boolean;
  title?: string;
  showTitle?: boolean;
  size?: 'small' | 'medium' | 'large';
  layout?: 'grid' | 'row';
}

const ColorPicker: React.FC<ColorPickerProps> = ({
  selectedColor,
  onColorSelect,
  isDark = false,
  title = 'Choose Color',
  showTitle = true,
  size = 'medium',
  layout = 'grid',
}) => {
  const theme = isDark ? darkTheme : lightTheme;

  // Available highlight colors with names
  const colorOptions = [
    { color: HighlightColor.YELLOW, name: 'Yellow' },
    { color: HighlightColor.GREEN, name: 'Green' },
    { color: HighlightColor.BLUE, name: 'Blue' },
    { color: HighlightColor.ORANGE, name: 'Orange' },
    { color: HighlightColor.PINK, name: 'Pink' },
    { color: HighlightColor.PURPLE, name: 'Purple' },
    { color: HighlightColor.RED, name: 'Red' },
    { color: HighlightColor.CYAN, name: 'Cyan' },
  ];

  // Get size dimensions
  const getSizeDimensions = () => {
    switch (size) {
      case 'small':
        return { width: 24, height: 24, borderRadius: 12 };
      case 'medium':
        return { width: 32, height: 32, borderRadius: 16 };
      case 'large':
        return { width: 40, height: 40, borderRadius: 20 };
      default:
        return { width: 32, height: 32, borderRadius: 16 };
    }
  };

  const sizeDimensions = getSizeDimensions();

  // Get container style based on layout
  const getContainerStyle = () => {
    const baseStyle = {
      gap: size === 'small' ? 8 : size === 'medium' ? 12 : 16,
    };

    if (layout === 'row') {
      return {
        ...baseStyle,
        flexDirection: 'row' as const,
        flexWrap: 'wrap' as const,
        justifyContent: 'center' as const,
      };
    }

    return {
      ...baseStyle,
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      justifyContent: 'center' as const,
      maxWidth: layout === 'grid' ? 200 : undefined,
    };
  };

  return (
    <View style={styles.container}>
      {showTitle && (
        <Text style={[styles.title, { color: theme.colors.onSurface }]}>
          {title}
        </Text>
      )}

      <View style={[styles.colorContainer, getContainerStyle()]}>
        {colorOptions.map(({ color, name }) => (
          <TouchableOpacity
            key={color}
            style={[
              styles.colorOption,
              sizeDimensions,
              { backgroundColor: color },
              selectedColor === color && [
                styles.selectedColor,
                { borderColor: theme.colors.onSurface },
              ],
            ]}
            onPress={() => onColorSelect(color)}
            activeOpacity={0.7}
            accessibilityLabel={`Select ${name} color`}
            accessibilityRole="button"
            accessibilityState={{ selected: selectedColor === color }}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
    textAlign: 'center',
  },
  colorContainer: {
    alignItems: 'center',
  },
  colorOption: {
    borderWidth: 2,
    borderColor: 'transparent',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  selectedColor: {
    borderWidth: 3,
    elevation: 4,
    shadowOpacity: 0.3,
    shadowRadius: 2,
  },
});

export default ColorPicker;
