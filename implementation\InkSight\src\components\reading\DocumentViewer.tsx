/**
 * Document Viewer Component
 * Core component for displaying documents across all 9 supported formats
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  ScrollView,
  Text,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import {
  DocumentViewerProps,
  DocumentViewerState,
  ReadingPosition,
  ReadingProgress,
} from '../../types/reading';
import { DocumentFormat } from '../../types/document';
import { ReadingServiceImpl } from '../../services/reading/ReadingService';

const { height: screenHeight } = Dimensions.get('window');

export const DocumentViewer: React.FC<DocumentViewerProps> = ({
  documentId,
  onLoadingChange,
  onError,
  onProgressChange,
  onNavigationChange,
  settings,
}) => {
  const [viewerState, setViewerState] = useState<DocumentViewerState | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const scrollViewRef = useRef<ScrollView>(null);
  const readingService = useRef(new ReadingServiceImpl());
  const readingStartTime = useRef<Date>(new Date());

  const loadDocument = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const state = await readingService.current.loadDocument(documentId);
      setViewerState(state);

      // Notify parent components
      onProgressChange?.(state.readingProgress);
      onNavigationChange?.(state.navigationState);

      // Restore reading position
      if (state.readingProgress.currentPosition.scrollPosition > 0) {
        setTimeout(() => {
          scrollViewRef.current?.scrollTo({
            y: state.readingProgress.currentPosition.scrollPosition,
            animated: false,
          });
        }, 100);
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to load document';
      setError(errorMessage);
      console.error('Document loading error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [documentId, onProgressChange, onNavigationChange]);

  // Load document on mount
  useEffect(() => {
    loadDocument();
  }, [loadDocument]);

  // Notify parent of loading state changes
  useEffect(() => {
    onLoadingChange?.(isLoading);
  }, [isLoading, onLoadingChange]);

  // Notify parent of errors
  useEffect(() => {
    if (error) {
      onError?.(error);
    }
  }, [error, onError]);

  const handleScroll = useCallback(
    (event: {
      nativeEvent: {
        contentOffset: { y: number };
        contentSize: { height: number };
      };
    }) => {
      if (!viewerState) return;

      const { contentOffset, contentSize } = event.nativeEvent;
      const scrollPosition = contentOffset.y;
      const scrollPercentage =
        contentSize.height > 0
          ? (scrollPosition / (contentSize.height - screenHeight)) * 100
          : 0;

      // Calculate character offset based on scroll position
      const characterOffset = Math.floor(
        (scrollPercentage / 100) * viewerState.content.text.length,
      );

      // Update reading position
      const newPosition: ReadingPosition = {
        documentId,
        scrollPosition,
        characterOffset,
        timestamp: new Date(),
        pageNumber: viewerState.navigationState.currentPage,
        chapterId: viewerState.navigationState.currentChapter,
      };

      // Calculate reading progress
      const progressPercentage = Math.min(100, Math.max(0, scrollPercentage));
      const readingTime = Math.floor(
        (new Date().getTime() - readingStartTime.current.getTime()) / 1000,
      );

      const updatedProgress: ReadingProgress = {
        ...viewerState.readingProgress,
        currentPosition: newPosition,
        readCharacters: characterOffset,
        progressPercentage,
        readingTime: viewerState.readingProgress.readingTime + readingTime,
        lastReadAt: new Date(),
      };

      // Update state
      setViewerState(prev =>
        prev
          ? {
              ...prev,
              readingProgress: updatedProgress,
            }
          : null,
      );

      // Notify parent and save progress
      onProgressChange?.(updatedProgress);
      readingService.current.saveReadingPosition(newPosition);

      // Reset reading time tracker
      readingStartTime.current = new Date();
    },
    [viewerState, documentId, onProgressChange],
  );

  const renderDocumentContent = () => {
    if (!viewerState) return null;

    const { metadata, content } = viewerState;
    const appliedSettings = {
      fontSize: 16,
      fontFamily: 'System',
      lineHeight: 1.5,
      backgroundColor: '#FFFFFF',
      textColor: '#000000',
      ...settings,
    };

    const contentStyle = {
      fontSize: appliedSettings.fontSize,
      fontFamily: appliedSettings.fontFamily,
      lineHeight: appliedSettings.fontSize * appliedSettings.lineHeight,
      color: appliedSettings.textColor,
      backgroundColor: appliedSettings.backgroundColor,
    };

    switch (metadata.format) {
      case DocumentFormat.EPUB:
      case DocumentFormat.HTML:
        return (
          <View style={styles.contentContainer}>
            {content.html ? (
              <Text style={[styles.htmlContent, contentStyle]}>
                {/* Note: In a real implementation, you'd use a WebView or HTML renderer */}
                {content.text}
              </Text>
            ) : (
              <Text style={[styles.textContent, contentStyle]}>
                {content.text}
              </Text>
            )}
          </View>
        );

      case DocumentFormat.PDF:
        return (
          <View style={styles.contentContainer}>
            <Text style={[styles.textContent, contentStyle]}>
              {/* Note: PDF rendering would use react-native-pdf in real implementation */}
              {content.text ||
                '[PDF Content - Full PDF rendering not yet implemented]'}
            </Text>
          </View>
        );

      case DocumentFormat.MD:
        return (
          <View style={styles.contentContainer}>
            <Text style={[styles.textContent, contentStyle]}>
              {content.text}
            </Text>
          </View>
        );

      default:
        return (
          <View style={styles.contentContainer}>
            <Text style={[styles.textContent, contentStyle]}>
              {content.text}
            </Text>
          </View>
        );
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6200EE" />
        <Text style={styles.loadingText}>Loading document...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Error loading document</Text>
        <Text style={styles.errorDetails}>{error}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        onScroll={handleScroll}
        scrollEventThrottle={100}
        showsVerticalScrollIndicator={true}
      >
        {renderDocumentContent()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#D32F2F',
    marginBottom: 8,
  },
  errorDetails: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  contentContainer: {
    padding: 20,
    minHeight: screenHeight,
  },
  textContent: {
    fontSize: 16,
    lineHeight: 24,
    color: '#000000',
  },
  htmlContent: {
    fontSize: 16,
    lineHeight: 24,
    color: '#000000',
  },
});
