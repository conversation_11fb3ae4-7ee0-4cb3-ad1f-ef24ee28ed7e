/**
 * Settings Redux Slice
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Settings state interface
export interface SettingsState {
  // Theme and appearance
  theme: 'light' | 'dark' | 'system';
  fontSize: number;
  fontFamily: string;
  lineHeight: number;

  // Reading preferences
  readingMode: 'scroll' | 'page';
  pageTransition: 'slide' | 'fade' | 'none';
  autoSave: boolean;
  autoSaveInterval: number; // in seconds

  // Handwriting recognition
  handwritingEnabled: boolean;
  handwritingLanguage: string;
  handwritingAccuracy: number;

  // Privacy and security
  biometricAuth: boolean;
  dataEncryption: boolean;
  offlineMode: boolean;

  // Performance
  maxCacheSize: number; // in MB
  preloadPages: number;

  // Accessibility
  highContrast: boolean;
  screenReader: boolean;
  largeText: boolean;

  // Application
  version: string;
  firstLaunch: boolean;
  lastUpdated: string;
}

// Initial state with privacy-first defaults
const initialState: SettingsState = {
  // Theme and appearance
  theme: 'system',
  fontSize: 16,
  fontFamily: 'System',
  lineHeight: 1.5,

  // Reading preferences
  readingMode: 'scroll',
  pageTransition: 'slide',
  autoSave: true,
  autoSaveInterval: 30,

  // Handwriting recognition
  handwritingEnabled: true,
  handwritingLanguage: 'en',
  handwritingAccuracy: 0.87,

  // Privacy and security (privacy-first defaults)
  biometricAuth: false,
  dataEncryption: true,
  offlineMode: true,

  // Performance
  maxCacheSize: 500,
  preloadPages: 3,

  // Accessibility
  highContrast: false,
  screenReader: false,
  largeText: false,

  // Application
  version: '1.0.0',
  firstLaunch: true,
  lastUpdated: new Date().toISOString(),
};

// Settings slice
const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    // Theme and appearance
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.theme = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    setFontSize: (state, action: PayloadAction<number>) => {
      state.fontSize = Math.max(12, Math.min(24, action.payload));
      state.lastUpdated = new Date().toISOString();
    },

    setFontFamily: (state, action: PayloadAction<string>) => {
      state.fontFamily = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    // Reading preferences
    setReadingMode: (state, action: PayloadAction<'scroll' | 'page'>) => {
      state.readingMode = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    setAutoSave: (state, action: PayloadAction<boolean>) => {
      state.autoSave = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    // Handwriting recognition
    setHandwritingEnabled: (state, action: PayloadAction<boolean>) => {
      state.handwritingEnabled = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    setHandwritingLanguage: (state, action: PayloadAction<string>) => {
      state.handwritingLanguage = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    // Privacy and security
    setBiometricAuth: (state, action: PayloadAction<boolean>) => {
      state.biometricAuth = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    setDataEncryption: (state, action: PayloadAction<boolean>) => {
      state.dataEncryption = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    // Accessibility
    setHighContrast: (state, action: PayloadAction<boolean>) => {
      state.highContrast = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    setLargeText: (state, action: PayloadAction<boolean>) => {
      state.largeText = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    // Application
    setFirstLaunch: (state, action: PayloadAction<boolean>) => {
      state.firstLaunch = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    // Bulk settings update
    updateSettings: (state, action: PayloadAction<Partial<SettingsState>>) => {
      Object.assign(state, action.payload);
      state.lastUpdated = new Date().toISOString();
    },

    // Reset to defaults
    resetSettings: () => initialState,
  },
});

// Export actions
export const {
  setTheme,
  setFontSize,
  setFontFamily,
  setReadingMode,
  setAutoSave,
  setHandwritingEnabled,
  setHandwritingLanguage,
  setBiometricAuth,
  setDataEncryption,
  setHighContrast,
  setLargeText,
  setFirstLaunch,
  updateSettings,
  resetSettings,
} = settingsSlice.actions;

// Export reducer
export default settingsSlice.reducer;
