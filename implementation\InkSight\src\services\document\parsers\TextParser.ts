/**
 * Text Parser Implementation
 * Handles plain text files with encoding detection
 */

import RNFS from 'react-native-fs';
import {
  DocumentFormat,
  DocumentParser,
  DocumentParseResult,
  DocumentParseOptions,
  DocumentMetadata,
  DocumentContent,
  ParsedDocument,
  DocumentError,
  DocumentProcessingError,
} from '../../../types/document';

export class TextParser implements DocumentParser {
  supportedFormats: DocumentFormat[] = [DocumentFormat.TXT, DocumentFormat.CSV];

  canParse(filePath: string, mimeType?: string): boolean {
    const extension = filePath.toLowerCase().split('.').pop();
    return (
      extension === 'txt' ||
      extension === 'csv' ||
      mimeType === 'text/plain' ||
      mimeType === 'text/csv'
    );
  }

  async validateDocument(filePath: string): Promise<boolean> {
    try {
      // Check if file exists and is readable
      const exists = await RNFS.exists(filePath);
      if (!exists) {
        return false;
      }

      // Check if it's a text file by trying to read a small portion
      const sample = await RNFS.read(filePath, 1024, 0, 'utf8');

      // Basic validation: check if content is readable text
      // Look for null bytes which would indicate binary content
      return !sample.includes('\u0000');
    } catch (error) {
      console.error('Text validation error:', error);
      return false;
    }
  }

  async parse(
    filePath: string,
    options: DocumentParseOptions = {
      extractText: true,
      extractMetadata: true,
      extractTableOfContents: false,
      generateThumbnail: false,
    },
  ): Promise<DocumentParseResult> {
    try {
      const metadata = await this.extractMetadataFromFile(filePath);
      let content: DocumentContent = { text: '' };

      // Extract content if requested
      if (options.extractText) {
        content = await this.extractContent(filePath, options);
      }

      const parsedDocument: ParsedDocument = {
        metadata,
        content,
      };

      return {
        success: true,
        document: parsedDocument,
      };
    } catch (error) {
      console.error('Text parsing error:', error);
      return {
        success: false,
        error: `Failed to parse text file: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      };
    }
  }

  async extractMetadata(filePath: string): Promise<DocumentMetadata> {
    try {
      return await this.extractMetadataFromFile(filePath);
    } catch (error) {
      throw new DocumentProcessingError(
        DocumentError.PARSING_ERROR,
        `Failed to extract text metadata: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        filePath,
        error instanceof Error ? error : undefined,
      );
    }
  }

  private async extractMetadataFromFile(
    filePath: string,
  ): Promise<DocumentMetadata> {
    const stats = await RNFS.stat(filePath);
    const extension = filePath.toLowerCase().split('.').pop();

    // Determine format based on extension
    const format =
      extension === 'csv' ? DocumentFormat.CSV : DocumentFormat.TXT;
    const mimeType = format === DocumentFormat.CSV ? 'text/csv' : 'text/plain';

    // Generate unique ID based on file path and modification time
    const id = this.generateDocumentId(
      filePath,
      stats.mtime.toString(),
      format,
    );

    // Extract basic text statistics
    const textStats = await this.extractTextStatistics(filePath);

    return {
      id,
      title: this.getFileNameWithoutExtension(filePath),
      fileSize: stats.size,
      format,
      mimeType,
      createdAt: new Date(stats.ctime),
      modifiedAt: new Date(stats.mtime),
      readingProgress: 0,
      totalReadingTime: 0,
      wordCount: textStats.wordCount,
      language: await this.detectLanguage(filePath),
    };
  }

  private async extractTextStatistics(filePath: string): Promise<{
    wordCount: number;
    lineCount: number;
    characterCount: number;
  }> {
    try {
      const content = await RNFS.readFile(filePath, 'utf8');

      const characterCount = content.length;
      const lineCount = content.split('\n').length;
      const wordCount = this.countWords(content);

      return {
        wordCount,
        lineCount,
        characterCount,
      };
    } catch (error) {
      console.warn('Failed to extract text statistics:', error);
      return {
        wordCount: 0,
        lineCount: 0,
        characterCount: 0,
      };
    }
  }

  private async detectLanguage(filePath: string): Promise<string> {
    try {
      // Read a sample of the file for language detection
      const sample = await RNFS.read(filePath, 2048, 0, 'utf8');

      // Basic language detection based on character patterns
      // This is a simplified implementation - in production, you might use a proper language detection library

      // Check for common English words
      const englishWords = [
        'the',
        'and',
        'or',
        'but',
        'in',
        'on',
        'at',
        'to',
        'for',
        'of',
        'with',
        'by',
      ];
      const englishCount = englishWords.reduce((count, word) => {
        const regex = new RegExp(`\\b${word}\\b`, 'gi');
        return count + (sample.match(regex) || []).length;
      }, 0);

      // Check for non-ASCII characters
      // eslint-disable-next-line no-control-regex
      const hasNonAscii = /[^\x00-\x7F]/.test(sample);

      if (englishCount > 5 && !hasNonAscii) {
        return 'en';
      }

      // Default to unknown
      return 'unknown';
    } catch {
      console.warn('Language detection failed');
      return 'unknown';
    }
  }

  private async extractContent(
    filePath: string,
    options: DocumentParseOptions,
  ): Promise<DocumentContent> {
    try {
      let content = await RNFS.readFile(filePath, options.encoding || 'utf8');

      // Apply text length limit if specified
      if (options.maxTextLength && content.length > options.maxTextLength) {
        content = content.substring(0, options.maxTextLength);
      }

      // For CSV files, provide basic structure information
      const extension = filePath.toLowerCase().split('.').pop();
      if (extension === 'csv') {
        const csvInfo = this.analyzeCSVStructure(content);
        content = `CSV File Analysis:\n${csvInfo}\n\n${content}`;
      }

      return {
        text: content,
      };
    } catch (error) {
      console.error('Failed to extract text content:', error);
      return {
        text: '',
      };
    }
  }

  private analyzeCSVStructure(content: string): string {
    try {
      const lines = content.split('\n').filter(line => line.trim());
      if (lines.length === 0) {
        return 'Empty CSV file';
      }

      const firstLine = lines[0];
      const delimiter = this.detectCSVDelimiter(firstLine);
      const columns = firstLine.split(delimiter);

      return [
        `Rows: ${lines.length}`,
        `Columns: ${columns.length}`,
        `Delimiter: "${delimiter}"`,
        `Headers: ${columns
          .map(col => col.trim().replace(/"/g, ''))
          .join(', ')}`,
      ].join('\n');
    } catch {
      return 'Unable to analyze CSV structure';
    }
  }

  private detectCSVDelimiter(line: string): string {
    const delimiters = [',', ';', '\t', '|'];
    let maxCount = 0;
    let bestDelimiter = ',';

    for (const delimiter of delimiters) {
      const count = (line.match(new RegExp(`\\${delimiter}`, 'g')) || [])
        .length;
      if (count > maxCount) {
        maxCount = count;
        bestDelimiter = delimiter;
      }
    }

    return bestDelimiter;
  }

  private countWords(text: string): number {
    return text
      .trim()
      .split(/\s+/)
      .filter(word => word.length > 0).length;
  }

  private generateDocumentId(
    filePath: string,
    modificationTime: string,
    format: DocumentFormat,
  ): string {
    const fileName = this.getFileNameWithoutExtension(filePath);
    const timestamp = new Date(modificationTime).getTime();
    return `${format}-${fileName}-${timestamp}`;
  }

  private getFileNameWithoutExtension(filePath: string): string {
    const fileName = filePath.split('/').pop() || filePath;
    const lastDotIndex = fileName.lastIndexOf('.');
    return lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName;
  }
}
