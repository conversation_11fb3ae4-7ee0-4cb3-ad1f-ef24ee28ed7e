/**
 * Zoom Controls Component
 * Provides zoom and pan functionality for document viewing
 */

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { ZoomControlsProps } from '../../types/reading';

export const ZoomControls: React.FC<ZoomControlsProps> = ({
  zoomState,
  onZoomChange,
  onFitToWidth,
  onFitToHeight,
  onFitToPage,
  onResetZoom,
  disabled = false,
}) => {
  const handleZoomIn = () => {
    if (disabled) return;

    const newLevel = Math.min(5.0, zoomState.level * 1.25);
    onZoomChange({
      ...zoomState,
      level: newLevel,
      fitMode: 'custom',
    });
  };

  const handleZoomOut = () => {
    if (disabled) return;

    const newLevel = Math.max(0.1, zoomState.level / 1.25);
    onZoomChange({
      ...zoomState,
      level: newLevel,
      fitMode: 'custom',
    });
  };

  const formatZoomLevel = (level: number): string => {
    return `${Math.round(level * 100)}%`;
  };

  const getFitModeLabel = (fitMode: string): string => {
    switch (fitMode) {
      case 'width':
        return 'Fit Width';
      case 'height':
        return 'Fit Height';
      case 'page':
        return 'Fit Page';
      default:
        return 'Custom';
    }
  };

  return (
    <View style={styles.container}>
      {/* Zoom Out Button */}
      <TouchableOpacity
        style={[
          styles.zoomButton,
          (disabled || zoomState.level <= 0.1) && styles.disabledButton,
        ]}
        onPress={handleZoomOut}
        disabled={disabled || zoomState.level <= 0.1}
      >
        <Text
          style={[
            styles.zoomButtonText,
            (disabled || zoomState.level <= 0.1) && styles.disabledText,
          ]}
        >
          −
        </Text>
      </TouchableOpacity>

      {/* Zoom Level Display */}
      <View style={styles.zoomInfo}>
        <Text style={styles.zoomLevel}>{formatZoomLevel(zoomState.level)}</Text>
        <Text style={styles.fitMode}>{getFitModeLabel(zoomState.fitMode)}</Text>
      </View>

      {/* Zoom In Button */}
      <TouchableOpacity
        style={[
          styles.zoomButton,
          (disabled || zoomState.level >= 5.0) && styles.disabledButton,
        ]}
        onPress={handleZoomIn}
        disabled={disabled || zoomState.level >= 5.0}
      >
        <Text
          style={[
            styles.zoomButtonText,
            (disabled || zoomState.level >= 5.0) && styles.disabledText,
          ]}
        >
          +
        </Text>
      </TouchableOpacity>

      {/* Fit Controls */}
      <View style={styles.fitControls}>
        <TouchableOpacity
          style={[
            styles.fitButton,
            zoomState.fitMode === 'width' && styles.activeFitButton,
            disabled && styles.disabledButton,
          ]}
          onPress={onFitToWidth}
          disabled={disabled}
        >
          <Text
            style={[
              styles.fitButtonText,
              zoomState.fitMode === 'width' && styles.activeFitButtonText,
              disabled && styles.disabledText,
            ]}
          >
            Width
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.fitButton,
            zoomState.fitMode === 'height' && styles.activeFitButton,
            disabled && styles.disabledButton,
          ]}
          onPress={onFitToHeight}
          disabled={disabled}
        >
          <Text
            style={[
              styles.fitButtonText,
              zoomState.fitMode === 'height' && styles.activeFitButtonText,
              disabled && styles.disabledText,
            ]}
          >
            Height
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.fitButton,
            zoomState.fitMode === 'page' && styles.activeFitButton,
            disabled && styles.disabledButton,
          ]}
          onPress={onFitToPage}
          disabled={disabled}
        >
          <Text
            style={[
              styles.fitButtonText,
              zoomState.fitMode === 'page' && styles.activeFitButtonText,
              disabled && styles.disabledText,
            ]}
          >
            Page
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.fitButton,
            styles.resetButton,
            disabled && styles.disabledButton,
          ]}
          onPress={onResetZoom}
          disabled={disabled}
        >
          <Text
            style={[
              styles.fitButtonText,
              styles.resetButtonText,
              disabled && styles.disabledText,
            ]}
          >
            Reset
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    margin: 8,
  },
  zoomButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#6200EE',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  disabledButton: {
    backgroundColor: '#E0E0E0',
  },
  zoomButtonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  disabledText: {
    color: '#9E9E9E',
  },
  zoomInfo: {
    alignItems: 'center',
    marginHorizontal: 16,
    minWidth: 80,
  },
  zoomLevel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  fitMode: {
    fontSize: 12,
    color: '#666666',
    marginTop: 2,
  },
  fitControls: {
    flexDirection: 'row',
    marginLeft: 16,
  },
  fitButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    backgroundColor: '#F5F5F5',
    marginHorizontal: 2,
  },
  activeFitButton: {
    backgroundColor: '#6200EE',
  },
  resetButton: {
    backgroundColor: '#FF6B6B',
  },
  fitButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666666',
  },
  activeFitButtonText: {
    color: '#FFFFFF',
  },
  resetButtonText: {
    color: '#FFFFFF',
  },
});
