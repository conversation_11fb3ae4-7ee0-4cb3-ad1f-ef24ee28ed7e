/**
 * Progress Tracker Component
 * Displays and manages reading progress for documents
 */

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { ProgressTrackerProps } from '../../types/reading';

export const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  progress,
  onPositionChange,
  showDetails = true,
  compact = false,
}) => {
  const formatReadingTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      return `${minutes}m`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  };

  const formatDate = (date: Date): string => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) {
      return 'Just now';
    } else if (diffMins < 60) {
      return `${diffMins}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const handleProgressBarPress = (event: {
    nativeEvent: { locationX: number };
  }) => {
    if (!onPositionChange) return;

    const { locationX } = event.nativeEvent;
    const progressBarWidth = 200; // Approximate width of progress bar
    const percentage = Math.max(
      0,
      Math.min(100, (locationX / progressBarWidth) * 100),
    );

    const newCharacterOffset = Math.floor(
      (percentage / 100) * progress.totalCharacters,
    );

    const newPosition = {
      ...progress.currentPosition,
      characterOffset: newCharacterOffset,
      timestamp: new Date(),
    };

    onPositionChange(newPosition);
  };

  if (compact) {
    return (
      <View style={styles.compactContainer}>
        <View style={styles.compactProgressBar}>
          <View
            style={[
              styles.compactProgressFill,
              { width: `${progress.progressPercentage}%` },
            ]}
          />
        </View>
        <Text style={styles.compactPercentage}>
          {Math.round(progress.progressPercentage)}%
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Progress Bar */}
      <TouchableOpacity
        style={styles.progressBarContainer}
        onPress={handleProgressBarPress}
        activeOpacity={0.8}
      >
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              { width: `${progress.progressPercentage}%` },
            ]}
          />
        </View>
        <Text style={styles.progressPercentage}>
          {Math.round(progress.progressPercentage)}%
        </Text>
      </TouchableOpacity>

      {/* Reading Details */}
      {showDetails && (
        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Reading Time:</Text>
            <Text style={styles.detailValue}>
              {formatReadingTime(progress.readingTime)}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Last Read:</Text>
            <Text style={styles.detailValue}>
              {formatDate(progress.lastReadAt)}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Progress:</Text>
            <Text style={styles.detailValue}>
              {progress.readCharacters.toLocaleString()} /{' '}
              {progress.totalCharacters.toLocaleString()} characters
            </Text>
          </View>

          {progress.totalPages && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Page:</Text>
              <Text style={styles.detailValue}>
                {progress.currentPosition.pageNumber || 1} /{' '}
                {progress.totalPages}
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    margin: 8,
  },
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  progressBarContainer: {
    marginBottom: 12,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#6200EE',
    borderRadius: 4,
  },
  compactProgressBar: {
    flex: 1,
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    overflow: 'hidden',
    marginRight: 8,
  },
  compactProgressFill: {
    height: '100%',
    backgroundColor: '#6200EE',
    borderRadius: 2,
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6200EE',
    textAlign: 'center',
  },
  compactPercentage: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6200EE',
    minWidth: 35,
    textAlign: 'right',
  },
  detailsContainer: {
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    paddingTop: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  detailValue: {
    fontSize: 14,
    color: '#000000',
    fontWeight: '400',
    textAlign: 'right',
    flex: 1,
    marginLeft: 16,
  },
});
