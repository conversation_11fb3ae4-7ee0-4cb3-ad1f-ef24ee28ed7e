/**
 * Settings Screen
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { MainTabScreenProps } from '../navigation/types';
import { useAppSelector, useAppDispatch } from '../store';
import { setTheme, setFontSize } from '../store/slices/settingsSlice';

type Props = MainTabScreenProps<'Settings'>;

const SettingsScreen: React.FC<Props> = ({ navigation: _navigation }) => {
  const dispatch = useAppDispatch();
  const settings = useAppSelector(state => state.settings);

  const handleThemeChange = () => {
    const themes: Array<'light' | 'dark' | 'system'> = [
      'light',
      'dark',
      'system',
    ];
    const currentIndex = themes.indexOf(settings.theme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];
    dispatch(setTheme(nextTheme));
  };

  const handleFontSizeChange = () => {
    const newSize =
      settings.fontSize === 16 ? 18 : settings.fontSize === 18 ? 20 : 16;
    dispatch(setFontSize(newSize));
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Settings</Text>
        <Text style={styles.subtitle}>Customize your InkSight experience</Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Appearance</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleThemeChange}
          >
            <Text style={styles.settingLabel}>Theme</Text>
            <Text style={styles.settingValue}>{settings.theme}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleFontSizeChange}
          >
            <Text style={styles.settingLabel}>Font Size</Text>
            <Text style={styles.settingValue}>{settings.fontSize}px</Text>
          </TouchableOpacity>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Font Family</Text>
            <Text style={styles.settingValue}>{settings.fontFamily}</Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Reading</Text>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Reading Mode</Text>
            <Text style={styles.settingValue}>{settings.readingMode}</Text>
          </View>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Auto Save</Text>
            <Text style={styles.settingValue}>
              {settings.autoSave ? 'Enabled' : 'Disabled'}
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Handwriting</Text>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Recognition</Text>
            <Text style={styles.settingValue}>
              {settings.handwritingEnabled ? 'Enabled' : 'Disabled'}
            </Text>
          </View>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Language</Text>
            <Text style={styles.settingValue}>
              {settings.handwritingLanguage}
            </Text>
          </View>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Accuracy</Text>
            <Text style={styles.settingValue}>
              {Math.round(settings.handwritingAccuracy * 100)}%
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy & Security</Text>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Data Encryption</Text>
            <Text style={styles.settingValue}>
              {settings.dataEncryption ? 'Enabled' : 'Disabled'}
            </Text>
          </View>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Offline Mode</Text>
            <Text style={styles.settingValue}>
              {settings.offlineMode ? 'Enabled' : 'Disabled'}
            </Text>
          </View>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Biometric Auth</Text>
            <Text style={styles.settingValue}>
              {settings.biometricAuth ? 'Enabled' : 'Disabled'}
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>About</Text>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Version</Text>
            <Text style={styles.settingValue}>{settings.version}</Text>
          </View>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Last Updated</Text>
            <Text style={styles.settingValue}>
              {new Date(settings.lastUpdated).toLocaleDateString()}
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#757575',
    marginBottom: 32,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#424242',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    marginBottom: 8,
  },
  settingLabel: {
    fontSize: 16,
    color: '#424242',
    fontWeight: '500',
  },
  settingValue: {
    fontSize: 16,
    color: '#6200EE',
    fontWeight: '600',
  },
});

export default SettingsScreen;
