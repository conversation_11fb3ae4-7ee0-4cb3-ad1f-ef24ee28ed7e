{"name": "InkSight", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "type-check": "tsc --noEmit"}, "dependencies": {"react": "18.3.1", "react-native": "0.80.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.25.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/metro-config": "0.80.0", "@react-native/typescript-config": "0.80.0", "@types/react": "^18.3.12", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^29.7.0", "eslint": "^8.19.0", "jest": "^29.7.0", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "npm@10.2.4"}