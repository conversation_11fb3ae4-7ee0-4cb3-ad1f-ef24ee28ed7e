/**
 * Annotation Type Definitions
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

// Annotation types
export enum AnnotationType {
  HIGHLIGHT = 'highlight',
  NOTE = 'note',
  BOOKMARK = 'bookmark',
  UNDERLINE = 'underline',
  STRIKETHROUGH = 'strikethrough',
}

// Highlight colors
export enum HighlightColor {
  YELLOW = '#FFEB3B',
  GREEN = '#4CAF50',
  BLUE = '#2196F3',
  ORANGE = '#FF9800',
  PINK = '#E91E63',
  PURPLE = '#9C27B0',
  RED = '#F44336',
  CYAN = '#00BCD4',
}

// Text selection interface
export interface TextSelection {
  startOffset: number;
  endOffset: number;
  selectedText: string;
  startContainer?: string;
  endContainer?: string;
  rangeData?: string; // Serialized range data for restoration
}

// Position information for annotations
export interface AnnotationPosition {
  page?: number; // For paginated documents
  chapter?: string; // For EPUB chapters
  section?: string; // For document sections
  xpath?: string; // XPath for precise positioning
  offset?: number; // Character offset in document
  coordinates?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

// Base annotation interface
export interface BaseAnnotation {
  id: string;
  type: AnnotationType;
  documentId: string;
  userId?: string;
  createdAt: string;
  updatedAt: string;
  position: AnnotationPosition;
  selection: TextSelection;
  metadata?: Record<string, unknown>;
}

// Highlight annotation
export interface HighlightAnnotation extends BaseAnnotation {
  type: AnnotationType.HIGHLIGHT;
  color: HighlightColor;
  opacity?: number; // 0-1, default 0.3
}

// Note annotation
export interface NoteAnnotation extends BaseAnnotation {
  type: AnnotationType.NOTE;
  title?: string;
  content: string;
  tags?: string[];
  category?: string;
  isPrivate?: boolean;
  attachments?: string[]; // File paths for attachments
}

// Bookmark annotation
export interface BookmarkAnnotation extends BaseAnnotation {
  type: AnnotationType.BOOKMARK;
  title: string;
  description?: string;
  category?: string;
}

// Underline annotation
export interface UnderlineAnnotation extends BaseAnnotation {
  type: AnnotationType.UNDERLINE;
  color?: string;
  style?: 'solid' | 'dashed' | 'dotted';
  thickness?: number;
}

// Strikethrough annotation
export interface StrikethroughAnnotation extends BaseAnnotation {
  type: AnnotationType.STRIKETHROUGH;
  color?: string;
  style?: 'solid' | 'dashed';
}

// Union type for all annotations
export type Annotation =
  | HighlightAnnotation
  | NoteAnnotation
  | BookmarkAnnotation
  | UnderlineAnnotation
  | StrikethroughAnnotation;

// Annotation collection interface
export interface AnnotationCollection {
  documentId: string;
  annotations: Annotation[];
  lastModified: string;
  version: number;
  checksum?: string; // For integrity verification
}

// Annotation search criteria
export interface AnnotationSearchCriteria {
  documentId?: string;
  type?: AnnotationType;
  color?: HighlightColor;
  tags?: string[];
  category?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  textQuery?: string;
  limit?: number;
  offset?: number;
}

// Annotation search result
export interface AnnotationSearchResult {
  annotations: Annotation[];
  totalCount: number;
  hasMore: boolean;
}

// Annotation export format
export enum AnnotationExportFormat {
  JSON = 'json',
  CSV = 'csv',
  HTML = 'html',
  MARKDOWN = 'markdown',
  PDF = 'pdf',
  TXT = 'txt',
}

// Annotation export options
export interface AnnotationExportOptions {
  format: AnnotationExportFormat;
  includeTypes?: AnnotationType[];
  includeMetadata?: boolean;
  includePosition?: boolean;
  groupBy?: 'type' | 'date' | 'document' | 'category';
  sortBy?: 'date' | 'position' | 'type';
  sortOrder?: 'asc' | 'desc';
}

// Annotation import result
export interface AnnotationImportResult {
  imported: number;
  skipped: number;
  errors: string[];
  duplicates: number;
}

// Annotation statistics
export interface AnnotationStatistics {
  totalAnnotations: number;
  byType: Record<AnnotationType, number>;
  byColor: Record<HighlightColor, number>;
  byDocument: Record<string, number>;
  byDate: Record<string, number>; // Date string -> count
  averagePerDocument: number;
  mostAnnotatedDocument: string;
  oldestAnnotation?: string; // Date string
  newestAnnotation?: string; // Date string
}

// Annotation conflict resolution
export interface AnnotationConflict {
  id: string;
  localAnnotation: Annotation;
  remoteAnnotation: Annotation;
  conflictType: 'content' | 'position' | 'metadata';
  resolution?: 'local' | 'remote' | 'merge';
}

// Annotation sync status
export interface AnnotationSyncStatus {
  documentId: string;
  lastSyncAt?: string;
  pendingChanges: number;
  conflicts: AnnotationConflict[];
  syncInProgress: boolean;
  lastError?: string;
}

// Annotation service configuration
export interface AnnotationServiceConfig {
  enableAutoSave: boolean;
  autoSaveInterval: number; // milliseconds
  maxAnnotationsPerDocument: number;
  enableConflictResolution: boolean;
  defaultHighlightColor: HighlightColor;
  defaultHighlightOpacity: number;
  enableEncryption: boolean;
  compressionEnabled: boolean;
}

// Annotation event types
export enum AnnotationEventType {
  CREATED = 'created',
  UPDATED = 'updated',
  DELETED = 'deleted',
  SELECTED = 'selected',
  DESELECTED = 'deselected',
  EXPORTED = 'exported',
  IMPORTED = 'imported',
}

// Annotation event
export interface AnnotationEvent {
  type: AnnotationEventType;
  annotation: Annotation;
  timestamp: string;
  metadata?: Record<string, unknown>;
}

// Annotation service interface
export interface IAnnotationService {
  // CRUD operations
  createAnnotation(
    annotation: Omit<Annotation, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<Annotation>;
  getAnnotation(id: string): Promise<Annotation | null>;
  updateAnnotation(
    id: string,
    updates: Partial<Annotation>,
  ): Promise<Annotation>;
  deleteAnnotation(id: string): Promise<boolean>;

  // Collection operations
  getAnnotationsForDocument(documentId: string): Promise<Annotation[]>;
  searchAnnotations(
    criteria: AnnotationSearchCriteria,
  ): Promise<AnnotationSearchResult>;
  getAnnotationStatistics(documentId?: string): Promise<AnnotationStatistics>;

  // Export/Import
  exportAnnotations(
    documentId: string,
    options: AnnotationExportOptions,
  ): Promise<string>;
  importAnnotations(
    data: string,
    format: AnnotationExportFormat,
  ): Promise<AnnotationImportResult>;

  // Sync operations
  syncAnnotations(documentId: string): Promise<AnnotationSyncStatus>;
  resolveConflict(
    conflictId: string,
    resolution: 'local' | 'remote' | 'merge',
  ): Promise<boolean>;

  // Event handling
  addEventListener(
    type: AnnotationEventType,
    listener: (event: AnnotationEvent) => void,
  ): void;
  removeEventListener(
    type: AnnotationEventType,
    listener: (event: AnnotationEvent) => void,
  ): void;
}

export default Annotation;
